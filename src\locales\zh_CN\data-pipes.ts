/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  id: 'Id',
  data_pipes: '数据管道',
  create: '创建',
  name: '名称',
  state: '状态',
  create_time: '创建时间',
  update_time: '更新时间e',
  operation: '操作',
  un_start: '取消开始',
  execute: '执行',
  edit: '编辑',
  publish: '发布',
  delete: '删除',
  save: '保存',
  cancel: '取消',
  script: '脚本',
  kill: '终止',
  stop: '停止',
  add: '添加',
  key: '键',
  value: '值',
  name_tips: '必填字段，数字，字母大小写，100个字符',
  data_pipes_delete_tips:
    '是否删除数据管道，删除后无法恢复',
  data_pipes_publish_tips: '是否发布数据管道',
  model_validate_tips: '必填字段'
}
