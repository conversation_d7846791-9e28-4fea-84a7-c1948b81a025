/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  instance_id: '实例 Id',
  job_id: '工作 Id',
  tasks: '任务',
  search: '搜索',
  success: '成功',
  fail: '失败',
  stop: '停止',
  running: '运行中',
  unknown: '未知',
  unpublished: '未发布',
  published: '发布',
  task_name: '任务名称',
  state: '状态',
  run_frequency: '运行频率',
  once: '一次',
  crontab: '定时',
  start_time: '开始时间',
  end_time: '结束时间',
  last_total_bytes: '最后总字节数',
  last_total_records: '最后总记录',
  rerun: '重新运行',
  kill: '终止',
  operation: '操作',
  view_log: '查看日志',
  log: '日志'
}
