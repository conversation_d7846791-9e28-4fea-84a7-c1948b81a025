/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import login from '@/locales/en_US/login'
import menu from '@/locales/en_US/menu'
import modal from '@/locales/en_US/modal'
import user_manage from '@/locales/en_US/user-manage'
import log from '@/locales/en_US/log'
import tasks from '@/locales/en_US/tasks'
import setting from '@/locales/en_US/setting'
import datasource from '@/locales/en_US/datasource'
import virtual_tables from '@/locales/en_US/virtual-tables'
import theme from '@/locales/en_US/theme'
import project from '@/locales/en_US/project'
import hook from '@/locales/en_US/hook'
import common from '@/locales/en_US/common'
import security from '@/locales/en_US/security'

export default {
  security,
  common,
  login,
  menu,
  modal,
  user_manage,
  log,
  tasks,
  setting,
  datasource,
  virtual_tables,
  theme,
  project,
  hook
}
