/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  user_manage: '用户管理',
  help: '帮助',
  setting: '设置',
  logout: '登出',
  tasks: '任务',
  datasource: '数据源',
  virtual_tables: '虚拟表',
  sync_task_definition: '同步任务定义',
  sync_task_instance: '同步任务实例',
  synchronization_instance: '同步任务实例',
}
