/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import login from '@/locales/zh_CN/login'
import menu from '@/locales/zh_CN/menu'
import modal from '@/locales/zh_CN/modal'
import user_manage from '@/locales/zh_CN/user-manage'
import log from '@/locales/zh_CN/log'
import tasks from '@/locales/zh_CN/tasks'
import setting from '@/locales/zh_CN/setting'
import datasource from '@/locales/zh_CN/datasource'
import virtual_tables from '@/locales/zh_CN/virtual-tables'
import theme from '@/locales/zh_CN/theme'
import project from '@/locales/zh_CN/project'
import hook from '@/locales/zh_CN/hook'
import common from '@/locales/zh_CN/common'
import security from '@/locales/zh_CN/security'

export default {
  security,
  common,
  login,
  menu,
  modal,
  user_manage,
  log,
  tasks,
  setting,
  datasource,
  virtual_tables,
  theme,
  project,
  hook
}
