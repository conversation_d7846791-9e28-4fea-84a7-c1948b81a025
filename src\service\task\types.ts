/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

interface TaskList {
  name?: string
  pageNo: number
  pageSize: number
}

interface TaskJobList {
  name: string
  pageNo: number
  pageSize: number
}

interface TaskRecycle {
  scriptId: number
  operatorId: number
}

interface TaskExecute {
  objectType: number
  executeType: number
}

interface JobDetail {
  createTime: string
  creatorName: string
  datapipeName: string
  jobId: number
  jobPlan: string
  jobStatus: string
  menderName: string
  publish: boolean
  updateTime: string
  status?: string
  instanceId?: number
}

export { TaskList, TaskJobList, TaskRecycle, TaskExecute, JobDetail }
