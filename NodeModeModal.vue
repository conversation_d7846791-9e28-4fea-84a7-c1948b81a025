<template>
  <div class="node-mode-modal">
    <el-row :gutter="12">
      <!-- 表名列表 -->
      <el-col :span="6" class="list-container">
        <div class="table-list">
          <h3>{{ $t('project.synchronization_definition.table_name') }}</h3>
          <div v-if="state.tables.length > 0" class="table-names">
            <div
              v-for="table in state.tables"
              :key="table"
              :class="['table-item', { 'active': table === state.currentTable }]"
              @click="onSwitchTable(table)"
            >
              <span class="table-name">{{ table }}</span>
            </div>
          </div>
          <el-empty v-else :description="$t('common.no_data')" />
        </div>
      </el-col>

      <!-- 输入表结构 -->
      <el-col :span="type === 'sink' ? 18 : 9">
        <div class="table-structure">
          <h3>{{ $t('project.synchronization_definition.input_table_structure') }}</h3>
          <el-table
            :data="state.inputTableData"
            size="small"
            border
            stripe
            :row-selection="columnSelectable"
            @selection-change="onSelectionChange"
            ref="inputTable"
          >
            <el-table-column
              v-if="columnSelectable"
              type="selection"
              width="55"
              :selectable="row => !row.unSupport"
            />
            <el-table-column
              prop="index"
              label="#"
              width="60"
              type="index"
              :index="index => index + 1"
            />
            <el-table-column
              prop="name"
              :label="$t('project.synchronization_definition.field_name')"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="type"
              :label="$t('project.synchronization_definition.field_type')"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              prop="nullable"
              :label="$t('project.synchronization_definition.non_empty')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.nullable ? $t('project.synchronization_definition.yes') : $t('project.synchronization_definition.no') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="primaryKey"
              :label="$t('project.synchronization_definition.primary_key')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.primaryKey ? $t('project.synchronization_definition.yes') : $t('project.synchronization_definition.no') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="comment"
              :label="$t('project.synchronization_definition.field_comment')"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="defaultValue"
              :label="$t('project.synchronization_definition.default_value')"
              min-width="100"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </el-col>

      <!-- 输出表结构 -->
      <el-col v-if="type !== 'sink'" :span="9">
        <div class="table-structure">
          <h3>{{ $t('project.synchronization_definition.output_table_structure') }}</h3>
          <el-table
            :data="state.outputTableData"
            size="small"
            border
            stripe
          >
            <el-table-column
              prop="index"
              label="#"
              width="60"
              type="index"
              :index="index => index + 1"
            />
            <!-- 根据不同的 transformType 显示不同的列 -->
            <template v-if="transformType === 'FieldMapper'">
              <el-table-column
                prop="original_field"
                :label="$t('project.synchronization_definition.original_field')"
                min-width="120"
              >
                <template slot-scope="scope">
                  <span :class="{ 'error-text': scope.row.isError }">
                    {{ scope.row.original_field }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                :label="$t('project.synchronization_definition.field_name')"
                min-width="120"
              >
                <template slot-scope="scope">
                  <div v-if="scope.row.isEdit" class="edit-field">
                    <el-input
                      v-model="scope.row.name"
                      size="mini"
                      @blur="scope.row.isEdit = false"
                      :class="{ 'is-error': !scope.row.name }"
                    />
                  </div>
                  <div v-else class="field-display">
                    <span>{{ scope.row.name }}</span>
                    <el-button
                      type="text"
                      size="mini"
                      icon="el-icon-edit"
                      @click="scope.row.isEdit = true"
                    />
                  </div>
                </template>
              </el-table-column>
            </template>
            <template v-else-if="transformType === 'MultiFieldSplit'">
              <el-table-column
                prop="original_field"
                :label="$t('project.synchronization_definition.original_field')"
                min-width="120"
              >
                <template slot-scope="scope">
                  <span :class="{ 'error-text': scope.row.isError }">
                    {{ scope.row.original_field }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                :label="$t('project.synchronization_definition.field_name')"
                min-width="120"
              >
                <template slot-scope="scope">
                  <span :class="{ 'error-text': isDuplicateName(scope.row.name) }">
                    {{ scope.row.name }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template v-else-if="transformType === 'Copy'">
              <el-table-column
                prop="original_field"
                :label="$t('project.synchronization_definition.original_field')"
                min-width="120"
              />
              <el-table-column
                prop="name"
                :label="$t('project.synchronization_definition.field_name')"
                min-width="120"
              >
                <template slot-scope="scope">
                  <div v-if="scope.row.copyTimes !== -1">
                    <span :class="{ 'error-text': isDuplicateName(scope.row.name) }">
                      {{ scope.row.name }}
                    </span>
                  </div>
                  <div v-else-if="scope.row.isEdit" class="edit-field">
                    <el-input
                      v-model="scope.row.name"
                      size="mini"
                      @blur="scope.row.isEdit = false"
                      :class="{ 'is-error': !scope.row.name }"
                    />
                  </div>
                  <div v-else class="field-display">
                    <span>{{ scope.row.name }}</span>
                    <el-button
                      type="text"
                      size="mini"
                      icon="el-icon-edit"
                      @click="scope.row.isEdit = true"
                    />
                  </div>
                </template>
              </el-table-column>
            </template>
            <template v-else>
              <el-table-column
                prop="name"
                :label="$t('project.synchronization_definition.field_name')"
                min-width="120"
                show-overflow-tooltip
              />
            </template>

            <el-table-column
              prop="type"
              :label="$t('project.synchronization_definition.field_type')"
              min-width="100"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ scope.row.outputDataType || scope.row.type }}
              </template>
            </el-table-column>
            <el-table-column
              prop="nullable"
              :label="$t('project.synchronization_definition.non_empty')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.nullable ? $t('project.synchronization_definition.yes') : $t('project.synchronization_definition.no') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="primaryKey"
              :label="$t('project.synchronization_definition.primary_key')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.primaryKey ? $t('project.synchronization_definition.yes') : $t('project.synchronization_definition.no') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="comment"
              :label="$t('project.synchronization_definition.field_comment')"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="defaultValue"
              :label="$t('project.synchronization_definition.default_value')"
              min-width="100"
              show-overflow-tooltip
            />

            <!-- 操作列 -->
            <el-table-column
              v-if="showOperationColumn"
              :label="$t('project.synchronization_definition.operation')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <div class="operation-buttons">
                  <!-- FieldMapper 操作 -->
                  <template v-if="transformType === 'FieldMapper'">
                    <el-dropdown @command="handleFieldMapperOperation">
                      <el-button size="mini" type="text">
                        <i class="el-icon-more"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          :command="{ action: 'moveToTop', index: scope.$index }"
                          :disabled="scope.$index === 0"
                        >
                          {{ $t('project.synchronization_definition.move_to_top') }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ action: 'moveToBottom', index: scope.$index }"
                          :disabled="scope.$index === state.outputTableData.length - 1"
                        >
                          {{ $t('project.synchronization_definition.move_to_bottom') }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ action: 'moveUp', index: scope.$index }"
                          :disabled="scope.$index === 0"
                        >
                          {{ $t('project.synchronization_definition.move_up') }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ action: 'moveDown', index: scope.$index }"
                          :disabled="scope.$index === state.outputTableData.length - 1"
                        >
                          {{ $t('project.synchronization_definition.move_down') }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    <el-popconfirm
                      :title="$t('project.synchronization_definition.delete_confirm')"
                      @confirm="deleteOutputRow(scope.$index)"
                    >
                      <el-button
                        slot="reference"
                        size="mini"
                        type="danger"
                        icon="el-icon-delete"
                        circle
                      />
                    </el-popconfirm>
                  </template>

                  <!-- MultiFieldSplit 操作 -->
                  <template v-else-if="transformType === 'MultiFieldSplit'">
                    <el-button
                      v-if="scope.row.isSplit"
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      circle
                      @click="deleteSplitField(scope.row, scope.$index)"
                    />
                    <el-tooltip
                      v-else
                      :content="$t('project.synchronization_definition.split')"
                      placement="top"
                    >
                      <el-button
                        size="mini"
                        type="primary"
                        icon="el-icon-rank"
                        circle
                        :disabled="scope.row.splitDisabled"
                        @click="handleSplit(scope.row)"
                      />
                    </el-tooltip>
                  </template>

                  <!-- Copy 操作 -->
                  <template v-else-if="transformType === 'Copy'">
                    <el-popconfirm
                      v-if="scope.row.copyTimes === -1"
                      :title="$t('project.synchronization_definition.delete_confirm')"
                      @confirm="deleteOutputRow(scope.$index)"
                    >
                      <el-button
                        slot="reference"
                        size="mini"
                        type="danger"
                        icon="el-icon-delete"
                        circle
                      />
                    </el-popconfirm>
                    <el-tooltip
                      v-else
                      :content="$t('project.synchronization_definition.copy')"
                      placement="top"
                    >
                      <el-button
                        size="mini"
                        type="primary"
                        icon="el-icon-copy-document"
                        circle
                        @click="handleCopy(scope.row)"
                      />
                    </el-tooltip>
                  </template>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>

    <!-- 分割字段弹窗 -->
    <el-dialog
      :title="$t('project.synchronization_definition.split_field')"
      :visible.sync="splitDialogVisible"
      width="500px"
      @close="closeSplitDialog"
    >
      <el-form :model="splitForm" label-width="120px">
        <el-form-item :label="$t('project.synchronization_definition.output_fields')">
          <el-input
            v-model="splitForm.outputFields"
            :placeholder="$t('project.synchronization_definition.split_fields_placeholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('project.synchronization_definition.separator')">
          <el-input
            v-model="splitForm.separator"
            :placeholder="$t('project.synchronization_definition.separator_placeholder')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="splitDialogVisible = false">
          {{ $t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="confirmSplit">
          {{ $t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'NodeModeModal',
  props: {
    type: {
      type: String,
      default: 'source'
    },
    transformType: {
      type: String,
      default: ''
    },
    predecessorsNodeId: {
      type: String,
      default: ''
    },
    currentNodeId: {
      type: String,
      default: ''
    },
    schemaError: {
      type: Object,
      default: () => ({})
    },
    refForm: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      state: {
        inputTableData: [],
        outputTableData: [],
        allTableData: [],
        currentTable: '',
        selectedKeys: [],
        tables: [],
        columnSelectable: false,
        database: '',
        datasourceInstanceId: '',
        datasourceName: '',
        format: '',
        transformOptions: {},
        secondTransformOptions: {},
        optionsOutputTableData: [],
        schemaError: {}
      },
      splitDialogVisible: false,
      splitForm: {
        outputFields: '',
        separator: '',
        originalField: ''
      },
      currentSplitRow: null,
      tempOutputTables: []
    }
  },
  computed: {
    columnSelectable() {
      return this.state.columnSelectable
    },
    showOperationColumn() {
      return ['FieldMapper', 'MultiFieldSplit', 'Copy'].includes(this.transformType)
    }
  },
  methods: {
    // 初始化数据
    initData(info) {
      this.state.currentTable = info.tables && info.tables.length ? info.tables[0] : ''
      this.state.tables = this.type === 'sink' ? [] : info.tables || []
      this.state.columnSelectable = info.columnSelectable || false
      this.state.database = info.database || ''
      this.state.datasourceInstanceId = info.datasourceInstanceId || ''
      this.state.format = info.format || ''
      this.state.transformOptions = info.transformOptions || {}
      this.state.secondTransformOptions = info.transformOptions || {}
      this.state.schemaError = this.schemaError || {}

      if (
        (info.sceneMode && info.sceneMode === 'SINGLE_TABLE') ||
        this.transformType === 'FieldMapper' ||
        this.transformType === 'MultiFieldSplit' ||
        this.transformType === 'Copy'
      ) {
        this.state.optionsOutputTableData = info.outputSchema || []
      }

      // 检查 debezium 格式
      if (this.state.format === 'COMPATIBLE_DEBEZIUM_JSON') {
        this.state.inputTableData = [
          { name: 'topic', type: 'string' },
          { name: 'key', type: 'string' },
          { name: 'value', type: 'string' }
        ]
        this.state.outputTableData = [
          { name: 'topic', type: 'string' },
          { name: 'key', type: 'string' },
          { name: 'value', type: 'string' }
        ]
      } else {
        if (
          (this.type === 'transform' || this.state.datasourceInstanceId) &&
          (this.type === 'transform' || this.state.database) &&
          ((this.type === 'sink' || this.type === 'transform') || this.state.tables.length)
        ) {
          this.getModelData()
        }
      }
    },

    // 获取模型数据
    async getModelData() {
      try {
        if (this.type === 'sink' || this.type === 'transform') {
          // SQL Transform 特殊处理
          if (this.transformType === 'Sql' && (!this.refForm || !this.refForm.getValues()?.query)) {
            return
          }

          // 请求前一个节点的数据
          if (this.predecessorsNodeId) {
            const res = await this.$api.queryTaskDetail(this.$route.params.jobDefinitionCode, this.predecessorsNodeId)
            this.state.tables = res.outputSchema.map(o => o.tableName)
            this.state.currentTable = res.outputSchema[0].tableName
            this.state.allTableData = [{
              database: res.outputSchema[0].database,
              tableInfos: res.outputSchema
            }]
            if (res.transformOptions) {
              this.state.transformOptions = res.transformOptions
            }
            this.onSwitchTable(this.state.currentTable)
          }
        } else {
          // Source 节点
          const res = await this.$api.modelInfo(this.state.datasourceInstanceId, [{
            database: this.state.database,
            tables: this.state.tables
          }])
          this.state.allTableData = res
          this.onSwitchTable(this.state.currentTable)
        }
      } catch (error) {
        console.error('获取模型数据失败:', error)
        this.$message.error('获取模型数据失败')
      }
    },

    // 切换表
    async onSwitchTable(table) {
      this.state.currentTable = table
      if (this.state.format === 'COMPATIBLE_DEBEZIUM_JSON') return

      if (this.state.allTableData && this.state.allTableData[0]) {
        const tableInfos = this.state.allTableData[0].tableInfos || []

        for (const t of tableInfos) {
          if (t.tableName === this.state.currentTable) {
            this.tempOutputTables = t.fields || []
            this.state.inputTableData = (t.fields || []).filter(f => !f.isSplit)

            if (this.state.columnSelectable) {
              this.handleColumnSelectable(t)
            } else {
              await this.handleTransformType(t)
            }
            break
          }
        }
      }
    },

    // 处理可选列
    handleColumnSelectable(t) {
      if (this.state.optionsOutputTableData && this.state.optionsOutputTableData[0] &&
          this.state.optionsOutputTableData[0].tableName === this.state.currentTable) {
        const result = this.state.optionsOutputTableData[0].fields ||
                      this.tempOutputTables.filter(row => this.state.selectedKeys.includes(row.name))
        this.state.outputTableData = result.filter(r => !r.unSupport)
      } else {
        this.state.outputTableData = this.tempOutputTables.filter(t => !t.unSupport)
      }
      this.state.selectedKeys = this.state.outputTableData.filter(o => !o.unSupport).map(o => o.name)
    },

    // 处理不同的转换类型
    async handleTransformType(t) {
      switch (this.transformType) {
        case 'FieldMapper':
          this.handleFieldMapper(t)
          break
        case 'MultiFieldSplit':
          this.handleMultiFieldSplit(t)
          break
        case 'Copy':
          this.handleCopy(t)
          break
        case 'Sql':
          await this.handleSqlTransform()
          break
        default:
          this.state.outputTableData = t.fields || []
      }
    },

    // 处理字段映射
    handleFieldMapper(t) {
      if (!this.state.secondTransformOptions && !this.state.transformOptions) {
        this.state.outputTableData = (this.state.inputTableData || []).map(i => ({
          ...i,
          original_field: i.name
        }))
        return
      }

      const transformOptions = this.state.transformOptions.changeOrders ?
                              this.state.transformOptions : this.state.secondTransformOptions

      this.state.outputTableData = this.state.optionsOutputTableData ?
        this.state.optionsOutputTableData[0].fields :
        (t.fields || []).map((f, i) => ({
          ...f,
          original_field: (transformOptions && Object.keys(transformOptions).length > 0) ?
            transformOptions.changeOrders[i].sourceFieldName : f.name
        }))

      this.state.outputTableData = this.state.outputTableData.map((o, i) => {
        if (!o.original_field && transformOptions && transformOptions.changeOrders) {
          o.original_field = transformOptions.changeOrders[i].sourceFieldName
        }
        return o
      })

      this.state.outputTableData.forEach(o => {
        o.isError = o.original_field === this.state.schemaError.fieldName
      })
    },

    // 处理多字段分割
    handleMultiFieldSplit(t) {
      this.state.outputTableData = this.state.optionsOutputTableData ?
        this.state.optionsOutputTableData[0].fields.map(f => {
          if (this.state.transformOptions.splits ||
              (this.state.secondTransformOptions && this.state.secondTransformOptions.splits)) {

            const transformOptions = this.state.transformOptions.splits ?
                                   this.state.transformOptions : this.state.secondTransformOptions
            const needSplitDisabled = transformOptions.splits.map(t => t.sourceFieldName)
            f.splitDisabled = needSplitDisabled.includes(f.name)

            transformOptions.splits.forEach(t => {
              if (t.outputFields.includes(f.name)) {
                f.original_field = t.sourceFieldName
                f.separator = t.separator
                f.isSplit = true
              }
            })

            if (!f.original_field) {
              f.original_field = f.name
            }
          }
          return f
        }) :
        (t.fields || []).map(f => ({
          ...f,
          original_field: f.name
        }))
    },

    // 处理复制字段
    handleCopy(t) {
      this.state.outputTableData = this.state.optionsOutputTableData ?
        this.state.optionsOutputTableData[0].fields.map(f => {
          if (this.state.transformOptions.copyList ||
              (this.state.secondTransformOptions && this.state.secondTransformOptions.copyList)) {

            const transformOptions = this.state.transformOptions.copyList ?
                                   this.state.transformOptions : this.state.secondTransformOptions

            transformOptions.copyList.forEach(t => {
              const inputTableData = this.state.inputTableData.map(i => i.name)

              if (!inputTableData.includes(f.name)) {
                f.copyTimes = -1
                if (t.targetFieldName === f.name) {
                  f.original_field = t.sourceFieldName
                }
              }
            })

            if (f.copyTimes !== -1) {
              f.original_field = f.name
            }
          }
          return f
        }) :
        (t.fields || []).map(f => ({
          ...f,
          original_field: f.name
        }))
    },

    // 处理 SQL 转换
    async handleSqlTransform() {
      try {
        const sqlQuery = this.refForm && this.refForm.getValues ? this.refForm.getValues().query : ''
        if (!sqlQuery) return

        const res = await this.$api.sqlModelInfo(
          this.$route.params.jobDefinitionCode,
          this.predecessorsNodeId,
          {
            sourceFieldName: null,
            query: sqlQuery
          }
        )
        this.state.outputTableData = res.fields || []
      } catch (error) {
        console.error('获取 SQL 转换输出数据失败:', error)
      }
    },

    // 选择变化处理
    onSelectionChange(selection) {
      this.state.selectedKeys = selection.map(row => row.name)
      this.state.outputTableData = this.tempOutputTables.filter(row =>
        this.state.selectedKeys.includes(row.name)
      )
    },

    // 检查重复名称
    isDuplicateName(name) {
      const names = this.state.outputTableData.map(o => o.name)
      return names.indexOf(name) !== names.lastIndexOf(name)
    },

    // 字段映射操作处理
    handleFieldMapperOperation(command) {
      const { action, index } = command
      const data = this.state.outputTableData

      switch (action) {
        case 'moveToTop':
          this.moveToTop(data, index)
          break
        case 'moveToBottom':
          this.moveToBottom(data, index)
          break
        case 'moveUp':
          this.changeIndex(data, index - 1, index)
          break
        case 'moveDown':
          this.changeIndex(data, index + 1, index)
          break
      }
    },

    // 移动到顶部
    moveToTop(list, index) {
      const item = list.splice(index, 1)[0]
      list.unshift(item)
    },

    // 移动到底部
    moveToBottom(list, index) {
      const item = list.splice(index, 1)[0]
      list.push(item)
    },

    // 交换位置
    changeIndex(list, targetIndex, sourceIndex) {
      const record = list[sourceIndex]
      list[sourceIndex] = list[targetIndex]
      list[targetIndex] = record
    },

    // 删除输出行
    deleteOutputRow(index) {
      this.state.outputTableData.splice(index, 1)
    },

    // 处理分割
    handleSplit(rowData) {
      this.currentSplitRow = rowData
      this.splitForm = {
        outputFields: '',
        separator: ',',
        originalField: rowData.name
      }
      this.splitDialogVisible = true
    },

    // 确认分割
    confirmSplit() {
      if (!this.splitForm.outputFields.trim()) {
        this.$message.warning('请输入输出字段')
        return
      }

      const outputFields = this.splitForm.outputFields.split(',').map(f => f.trim()).filter(f => f)

      outputFields.forEach(field => {
        this.state.outputTableData.push({
          ...this.currentSplitRow,
          name: field,
          isSplit: true,
          separator: this.splitForm.separator,
          original_field: this.splitForm.originalField
        })
      })

      // 禁用原字段的分割按钮
      this.currentSplitRow.splitDisabled = true

      this.splitDialogVisible = false
    },

    // 删除分割字段
    deleteSplitField(row, index) {
      // 删除所有相同原字段的分割字段
      const originalField = row.original_field
      this.state.outputTableData = this.state.outputTableData.filter(item =>
        !(item.original_field === originalField && item.isSplit)
      )

      // 重新启用原字段的分割按钮
      const originalRow = this.state.outputTableData.find(item =>
        item.name === originalField && !item.isSplit
      )
      if (originalRow) {
        originalRow.splitDisabled = false
      }
    },

    // 处理复制
    handleCopy(rowData) {
      const copyName = `${rowData.name}_copy`
      this.state.outputTableData.push({
        ...rowData,
        name: copyName,
        copyTimes: -1,
        original_field: rowData.name,
        isEdit: false
      })
    },

    // 关闭分割弹窗
    closeSplitDialog() {
      this.splitDialogVisible = false
      this.currentSplitRow = null
      this.splitForm = {
        outputFields: '',
        separator: '',
        originalField: ''
      }
    },

    // 设置选中字段
    setSelectFields(selectedKeys) {
      this.state.selectedKeys = selectedKeys || []
      this.$nextTick(() => {
        if (this.$refs.inputTable) {
          // 清除所有选择
          this.$refs.inputTable.clearSelection()
          // 根据 selectedKeys 设置选中状态
          this.state.inputTableData.forEach(row => {
            if (this.state.selectedKeys.includes(row.name)) {
              this.$refs.inputTable.toggleRowSelection(row, true)
            }
          })
        }
      })
    },

    // 获取选中字段
    getSelectFields() {
      return {
        tableFields: this.state.selectedKeys,
        all: this.state.selectedKeys.length === this.state.inputTableData.length
      }
    },

    // 获取输出结构
    getOutputSchema() {
      return {
        allTableData: this.state.allTableData,
        outputTableData: this.state.outputTableData,
        inputTableData: this.state.inputTableData
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.node-mode-modal {
  .list-container {
    .table-list {
      h3 {
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }

      .table-names {
        max-height: calc(100vh - 200px);
        overflow-y: auto;
        border: 1px solid #dcdfe6;
        border-radius: 4px;

        .table-item {
          padding: 12px 16px;
          border-bottom: 1px solid #ebeef5;
          cursor: pointer;
          transition: all 0.3s;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #ecf5ff;
            color: #409eff;
            border-color: #409eff;
          }

          .table-name {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .table-structure {
    h3 {
      margin-bottom: 16px;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .el-table {
      border-radius: 4px;
      overflow: hidden;
    }
  }

  .edit-field {
    .el-input.is-error {
      .el-input__inner {
        border-color: #f56c6c;
      }
    }
  }

  .field-display {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-button {
      margin-left: 8px;
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:hover .el-button {
      opacity: 1;
    }
  }

  .operation-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    .el-button {
      margin: 0;
    }
  }

  .error-text {
    color: #f56c6c;
  }

  // 表格行高度调整
  ::v-deep .el-table__row {
    height: 48px;
  }

  ::v-deep .el-table__header-wrapper {
    .el-table__header {
      th {
        height: 48px;
      }
    }
  }

  // 弹窗样式
  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 12px;
    }
  }

  // 空状态样式
  .el-empty {
    padding: 40px 0;
  }
}

// 全局样式覆盖
::v-deep .el-table {
  .el-table__empty-block {
    min-height: 200px;
  }

  .el-table__body-wrapper {
    max-height: calc(100vh - 300px);
    overflow-y: auto;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .node-mode-modal {
    .list-container {
      .table-names {
        max-height: calc(100vh - 250px);
      }
    }
  }
}
</style>
