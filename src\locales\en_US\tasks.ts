/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  instance_id: 'Instance Id',
  job_id: 'Job Id',
  tasks: 'Tasks',
  search: 'Search',
  success: 'Success',
  fail: 'Fail',
  stop: 'Stop',
  running: 'Running',
  unknown: 'Unknown',
  unpublished: 'Unpublished',
  published: 'Published',
  task_name: 'Task Name',
  state: 'State',
  run_frequency: 'Run Frequency',
  once: 'Once',
  crontab: 'Crontab',
  start_time: 'Start Time',
  end_time: 'End Time',
  last_total_bytes: 'Last Total Bytes',
  last_total_records: 'Last Total Records',
  rerun: 'Rerun',
  kill: 'Kill',
  operation: 'Operation',
  view_log: 'View Log',
  log: 'Log',
  view: 'View',
}
