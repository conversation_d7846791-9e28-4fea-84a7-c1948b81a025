/**
 * NodeModeModal 组件所需的 API 服务
 * 这是一个适配 Vue2 项目的 API 服务文件
 */

import axios from 'axios'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 添加认证 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

/**
 * 同步任务定义相关 API
 */
export const syncTaskDefinitionApi = {
  /**
   * 查询任务详情
   * @param {string} jobCode - 作业代码
   * @param {string} taskCode - 任务代码
   * @returns {Promise} 任务详情
   */
  queryTaskDetail(jobCode, taskCode) {
    return apiClient.get(`/job/task/${jobCode}`, {
      params: { pluginId: taskCode }
    })
  },

  /**
   * 获取模型信息
   * @param {string} datasourceId - 数据源ID
   * @param {Array} data - 数据库和表信息
   * @returns {Promise} 模型信息
   */
  modelInfo(datasourceId, data) {
    return apiClient.post(`/datasource/schemas`, data, {
      params: { datasourceId }
    })
  },

  /**
   * 获取 SQL 模型信息
   * @param {string} taskId - 任务ID
   * @param {string} pluginId - 插件ID
   * @param {Object} data - SQL 查询数据
   * @returns {Promise} SQL 模型信息
   */
  sqlModelInfo(taskId, pluginId, data) {
    return apiClient.post(`/schema/derivation/sql`, data, {
      params: {
        jobVersionId: taskId,
        inputPluginId: pluginId
      }
    })
  },

  /**
   * 获取定义配置
   * @param {string} jobCode - 作业代码
   * @returns {Promise} 定义配置
   */
  getDefinitionConfig(jobCode) {
    return apiClient.get(`/job/config/${jobCode}`)
  },

  /**
   * 保存任务定义项
   * @param {string} jobCode - 作业代码
   * @param {Object} data - 任务数据
   * @returns {Promise} 保存结果
   */
  saveTaskDefinitionItem(jobCode, data) {
    return apiClient.post(`/job/task/${jobCode}`, data)
  },

  /**
   * 获取输入表结构
   * @param {string} datasourceId - 数据源ID
   * @param {string} databaseName - 数据库名
   * @param {string} tableName - 表名
   * @returns {Promise} 表结构
   */
  getInputTableSchema(datasourceId, databaseName, tableName) {
    return apiClient.get('/datasource/schema', {
      params: {
        datasourceId,
        databaseName,
        tableName
      }
    })
  },

  /**
   * 获取输出表结构
   * @param {string} pluginName - 插件名
   * @param {Object} data - 配置数据
   * @returns {Promise} 输出表结构
   */
  getOutputTableSchema(pluginName, data) {
    return apiClient.post(`/job/table/schema`, data, {
      params: { pluginName }
    })
  }
}

/**
 * Vue 插件安装函数
 * 使用方式：Vue.use(NodeModeModalApi)
 */
export default {
  install(Vue) {
    // 将 API 方法挂载到 Vue 原型上
    Vue.prototype.$api = {
      ...syncTaskDefinitionApi
    }

    // 也可以作为全局属性使用
    Vue.prototype.$syncTaskApi = syncTaskDefinitionApi
  }
}

/**
 * 使用示例：
 * 
 * 1. 在 main.js 中安装插件：
 * import NodeModeModalApi from './api-service'
 * Vue.use(NodeModeModalApi)
 * 
 * 2. 在组件中使用：
 * async mounted() {
 *   try {
 *     const result = await this.$api.queryTaskDetail('job123', 'task456')
 *     console.log(result)
 *   } catch (error) {
 *     console.error('API调用失败:', error)
 *   }
 * }
 * 
 * 3. 或者直接导入使用：
 * import { syncTaskDefinitionApi } from './api-service'
 * 
 * const result = await syncTaskDefinitionApi.queryTaskDetail('job123', 'task456')
 */

/**
 * 错误处理工具函数
 */
export const apiErrorHandler = {
  /**
   * 处理 API 错误
   * @param {Error} error - 错误对象
   * @param {Object} vm - Vue 实例
   * @param {string} defaultMessage - 默认错误消息
   */
  handleError(error, vm, defaultMessage = '操作失败') {
    let message = defaultMessage
    
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = data.message || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 可以在这里处理登录跳转
          break
        case 403:
          message = '权限不足'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data.message || defaultMessage
      }
    } else if (error.request) {
      // 网络错误
      message = '网络连接失败，请检查网络'
    }

    // 显示错误消息
    if (vm && vm.$message) {
      vm.$message.error(message)
    } else {
      console.error(message)
    }

    return message
  }
}

/**
 * 数据转换工具函数
 */
export const dataTransformUtils = {
  /**
   * 转换表字段数据格式
   * @param {Array} fields - 原始字段数据
   * @returns {Array} 转换后的字段数据
   */
  transformTableFields(fields) {
    if (!Array.isArray(fields)) return []
    
    return fields.map(field => ({
      name: field.fieldName || field.name || '',
      type: field.fieldType || field.type || 'string',
      comment: field.fieldComment || field.comment || '',
      nullable: Boolean(field.nullable),
      primaryKey: Boolean(field.primaryKey),
      defaultValue: field.defaultValue || '',
      format: field.format || '',
      // 扩展字段
      isEdit: false,
      isSplit: false,
      copyTimes: 0,
      separator: '',
      original_field: field.original_field || field.name || '',
      splitDisabled: false,
      unSupport: Boolean(field.unSupport),
      outputDataType: field.outputDataType || ''
    }))
  },

  /**
   * 转换表信息数据格式
   * @param {Object} tableInfo - 原始表信息
   * @returns {Object} 转换后的表信息
   */
  transformTableInfo(tableInfo) {
    return {
      tableName: tableInfo.tableName || '',
      database: tableInfo.database || '',
      fields: this.transformTableFields(tableInfo.fields || [])
    }
  },

  /**
   * 转换模型数据格式
   * @param {Array} modelData - 原始模型数据
   * @returns {Array} 转换后的模型数据
   */
  transformModelData(modelData) {
    if (!Array.isArray(modelData)) return []
    
    return modelData.map(item => ({
      database: item.database || '',
      tableInfos: Array.isArray(item.tableInfos) 
        ? item.tableInfos.map(table => this.transformTableInfo(table))
        : []
    }))
  }
}
