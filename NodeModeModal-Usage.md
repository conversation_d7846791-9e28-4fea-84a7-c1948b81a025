# NodeModeModal 组件使用说明

## 概述

NodeModeModal 是一个用于显示和管理数据表输入输出结构的 Vue2 + Element UI 组件，从原 Vue3 + Naive UI 项目迁移而来。

## 功能特性

- 📊 **表结构展示**: 支持输入表和输出表结构的并排显示
- 🔄 **字段转换**: 支持多种字段转换类型（FieldMapper、MultiFieldSplit、Copy、Sql）
- ✅ **字段选择**: 支持可选择的字段列表
- ✂️ **字段分割**: 支持将单个字段分割为多个字段
- 📋 **字段复制**: 支持字段复制功能
- 🎯 **字段映射**: 支持字段重命名和映射
- 🔍 **错误提示**: 支持字段错误状态显示

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | String | 'source' | 节点类型：'source'、'sink'、'transform' |
| transformType | String | '' | 转换类型：'FieldMapper'、'MultiFieldSplit'、'Copy'、'Sql' |
| predecessorsNodeId | String | '' | 前置节点ID |
| currentNodeId | String | '' | 当前节点ID |
| schemaError | Object | {} | 模式错误信息 |
| refForm | Object | null | 表单引用对象 |

## 方法

### 公开方法

```javascript
// 初始化数据
this.$refs.nodeModeModal.initData({
  tables: ['table1', 'table2'],
  columnSelectable: true,
  database: 'test_db',
  datasourceInstanceId: 'ds_001',
  format: 'DEFAULT',
  transformOptions: {},
  outputSchema: []
})

// 设置选中字段
this.$refs.nodeModeModal.setSelectFields(['field1', 'field2'])

// 获取选中字段
const selectedFields = this.$refs.nodeModeModal.getSelectFields()
// 返回: { tableFields: ['field1', 'field2'], all: false }

// 获取输出结构
const outputSchema = this.$refs.nodeModeModal.getOutputSchema()
// 返回: { allTableData: [], outputTableData: [], inputTableData: [] }
```

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <NodeModeModal
      ref="nodeModeModal"
      :type="nodeType"
      :transform-type="transformType"
      :predecessors-node-id="predecessorsNodeId"
      :current-node-id="currentNodeId"
      :schema-error="schemaError"
      :ref-form="formRef"
    />
  </div>
</template>

<script>
import NodeModeModal from './NodeModeModal.vue'

export default {
  components: {
    NodeModeModal
  },
  data() {
    return {
      nodeType: 'source',
      transformType: '',
      predecessorsNodeId: '',
      currentNodeId: 'node_001',
      schemaError: {},
      formRef: null
    }
  },
  mounted() {
    // 初始化数据
    this.$refs.nodeModeModal.initData({
      tables: ['users', 'orders'],
      columnSelectable: true,
      database: 'ecommerce',
      datasourceInstanceId: 'mysql_001',
      format: 'DEFAULT'
    })
  }
}
</script>
```

### 字段映射模式

```vue
<template>
  <NodeModeModal
    ref="nodeModeModal"
    type="transform"
    transform-type="FieldMapper"
    :predecessors-node-id="predecessorsNodeId"
    :current-node-id="currentNodeId"
    :schema-error="schemaError"
    :ref-form="formRef"
  />
</template>

<script>
export default {
  data() {
    return {
      predecessorsNodeId: 'source_001',
      currentNodeId: 'transform_001',
      schemaError: { fieldName: 'user_id' },
      formRef: {
        getValues: () => ({ query: 'SELECT * FROM users' })
      }
    }
  }
}
</script>
```

### 字段分割模式

```vue
<template>
  <NodeModeModal
    ref="nodeModeModal"
    type="transform"
    transform-type="MultiFieldSplit"
    :predecessors-node-id="predecessorsNodeId"
    :current-node-id="currentNodeId"
  />
</template>
```

## API 接口依赖

组件依赖以下 API 接口，需要在你的项目中实现：

```javascript
// 在 Vue 实例中注入 $api 对象
Vue.prototype.$api = {
  // 查询任务详情
  queryTaskDetail(jobCode, taskCode) {
    return axios.get(`/job/task/${jobCode}?pluginId=${taskCode}`)
  },
  
  // 获取模型信息
  modelInfo(datasourceId, data) {
    return axios.post(`/datasource/schemas?datasourceId=${datasourceId}`, data)
  },
  
  // 获取 SQL 模型信息
  sqlModelInfo(taskId, pluginId, data) {
    return axios.post(`/schema/derivation/sql?jobVersionId=${taskId}&inputPluginId=${pluginId}`, data)
  }
}
```

## 国际化支持

组件使用 `this.$t()` 进行国际化，需要配置以下翻译键：

```javascript
// zh-CN
{
  'project.synchronization_definition.table_name': '表名',
  'project.synchronization_definition.input_table_structure': '输入表结构',
  'project.synchronization_definition.output_table_structure': '输出表结构',
  'project.synchronization_definition.field_name': '字段名',
  'project.synchronization_definition.field_type': '字段类型',
  'project.synchronization_definition.non_empty': '非空',
  'project.synchronization_definition.primary_key': '主键',
  'project.synchronization_definition.field_comment': '字段注释',
  'project.synchronization_definition.default_value': '默认值',
  'project.synchronization_definition.original_field': '原字段',
  'project.synchronization_definition.operation': '操作',
  'project.synchronization_definition.yes': '是',
  'project.synchronization_definition.no': '否',
  'project.synchronization_definition.split_field': '分割字段',
  'project.synchronization_definition.output_fields': '输出字段',
  'project.synchronization_definition.separator': '分隔符',
  'project.synchronization_definition.split_fields_placeholder': '请输入字段名，用逗号分隔',
  'project.synchronization_definition.separator_placeholder': '请输入分隔符',
  'project.synchronization_definition.delete_confirm': '确定删除吗？',
  'project.synchronization_definition.split': '分割',
  'project.synchronization_definition.copy': '复制',
  'project.synchronization_definition.move_to_top': '移到顶部',
  'project.synchronization_definition.move_to_bottom': '移到底部',
  'project.synchronization_definition.move_up': '上移',
  'project.synchronization_definition.move_down': '下移',
  'common.no_data': '暂无数据',
  'common.cancel': '取消',
  'common.confirm': '确定'
}
```

## 样式定制

组件使用 SCSS 编写样式，支持以下 CSS 变量定制：

```scss
// 主题色
--primary-color: #409eff;
--success-color: #67c23a;
--warning-color: #e6a23c;
--danger-color: #f56c6c;

// 边框色
--border-color: #dcdfe6;
--border-color-light: #ebeef5;

// 文字色
--text-color-primary: #303133;
--text-color-regular: #606266;

// 背景色
--background-color: #f5f7fa;
--background-color-light: #fafafa;
```

## 注意事项

1. **Vue Router**: 组件内部使用 `this.$route.params.jobDefinitionCode`，确保路由参数正确
2. **Element UI**: 确保项目中已正确安装和配置 Element UI
3. **国际化**: 确保项目中已配置 vue-i18n 或类似的国际化方案
4. **API 接口**: 需要根据实际后端接口调整 API 调用方法
5. **样式兼容**: 可能需要根据项目的整体样式进行微调

## 迁移对比

| 原 Vue3 + Naive UI | 新 Vue2 + Element UI |
|-------------------|---------------------|
| NGrid, NGridItem | el-row, el-col |
| NDataTable | el-table |
| NEmpty | el-empty |
| NSpace | div + flex 布局 |
| NButton | el-button |
| NDropdown | el-dropdown |
| NPopconfirm | el-popconfirm |
| NTooltip | el-tooltip |
| NDialog | el-dialog |
| NForm, NFormItem | el-form, el-form-item |
| NInput | el-input |

这个迁移版本保持了原组件的所有核心功能，同时适配了 Vue2 + Element UI 的语法和组件体系。
