/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  id: 'Id',
  user_manage: '用户管理',
  create: '创建',
  username: '用户名',
  state: '状态',
  create_time: '创建时间',
  update_time: '更新时间',
  operation: '操作',
  enable: '启用',
  disable: '禁用',
  edit: '编辑',
  delete: '删除',
  active: '激活',
  inactive: '未激活',
  password: '密码',
  model_validate_tips: '必填字段',
  username_tips: '必填字段，数字，字母大小写，50 个字符',
  password_tips: '必填字段，数字，字母大小写，6 个字符',
  user_delete_tips:
    '是否删除用户？ 删除后无法恢复'
}
