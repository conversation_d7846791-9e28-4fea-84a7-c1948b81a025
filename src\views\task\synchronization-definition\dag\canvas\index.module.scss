/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.container {
  height: 100%;
  width: 100%;
}

.dag-container {
  height: 100%;
  width: 100%;
}

.minimap {
  position: absolute;
  right: 20px;
  bottom: 60px;
  border: dashed 1px #e4e4e4;
}

.dag-node {
  display: flex;
  align-items: center;
  height: 100%;
  background-color: #fff;
  border: 1px solid #c2c8d5;
  border-radius: 4px;
  box-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.06);

  .dag-node-icon {
    width: 20px;
    height: 20px;
    margin: 0 10px;
  }

  .dag-node-label {
    width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    color: #666;
    font-size: 12px;
  }
}
