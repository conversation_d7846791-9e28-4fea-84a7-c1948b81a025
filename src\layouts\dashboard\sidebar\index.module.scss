/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
.btn-box {
  width: 168px;
  height: 60px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 0 14px;
  border-radius: 8px;
  margin-top: 10px;
  .projectinfo {
    width: 120px;
    // background-color: aquamarine;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .projectname {
    margin-left: 15px;
    display: flex;
    margin-top: 5px;
    align-items: center;
    font-size: 16px;
  }
  .name-space {
    margin-left: 5px;
  }
  .workflows {
    display: flex;
    align-items: center;
    margin-left: 30px;
    color: #6c6c6c;
  }
}

.dark {
  color: #eee;
}

.dark-blue {
  color: #eee !important;
}

.dark-blue-active {
  background-color: #ffffff10;
}

.dark-active {
  background-color: #2c2c2f;
}

.light-active {
  background-color: #eeeeee;
}

.light-none {
  background-color: transparent;
}

.collapsed-icon {
  width: 100%;
  height: 100%;
  display: flex;
  margin-top: 20px;
  justify-content: center;
  align-items: center;
  color: #d6d6d6;
}
