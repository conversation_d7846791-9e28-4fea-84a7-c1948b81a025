/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  tenant: {
    tenant_manage: 'Tenant Manage',
    create_tenant: 'Create Tenant',
    search_tips: 'Please enter keywords',
    tenant_code: 'Operating System Tenant',
    tenant_id: 'Tenant ID',
    description: 'Description',
    queue_name: 'QueueName',
    create_time: 'Create Time',
    update_time: 'Update Time',
    actions: 'Operation',
    edit_tenant: 'Edit Tenant',
    tenant_code_tips: 'Please enter the operating system tenant',
    queue_name_tips: 'Please select queue',
    description_tips: 'Please enter a description',
    delete_confirm: 'Delete?',
    edit: 'Edit',
    delete: 'Delete'
  },
  alarm_group: {
    create_alarm_group: 'Create Alarm Group',
    edit_alarm_group: 'Edit Alarm Group',
    search_tips: 'Please enter keywords',
    alert_group_name_tips: 'Please enter your alert group name',
    alarm_plugin_instance: 'Alarm Plugin Instance',
    alarm_plugin_instance_tips: 'Please select alert plugin instance',
    alarm_group_description_tips: 'Please enter your alarm group description',
    alert_group_name: 'Alert Group Name',
    alarm_group_description: 'Alarm Group Description',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    delete_confirm: 'Delete?',
    edit: 'Edit',
    delete: 'Delete'
  },
  worker_group: {
    create_worker_group: 'Create Worker Group',
    edit_worker_group: 'Edit Worker Group',
    search_tips: 'Please enter keywords',
    operation: 'Operation',
    delete_confirm: 'Delete?',
    edit: 'Edit',
    delete: 'Delete',
    group_name: 'Group Name',
    group_name_tips: 'Please enter your group name',
    worker_addresses: 'Worker Addresses',
    worker_addresses_tips: 'Please select worker addresses',
    create_time: 'Create Time',
    update_time: 'Update Time',
    description: 'Description',
    description_tips: 'Please enter description'
  },
  yarn_queue: {
    create_queue: 'Create Queue',
    edit_queue: 'Edit Queue',
    search_tips: 'Please enter keywords',
    queue_name: 'Queue Name',
    queue_value: 'Queue Value',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    queue_name_tips: 'Please enter your queue name(case-insensitive)',
    queue_value_tips: 'Please enter your queue value'
  },
  environment: {
    create_environment: 'Create Environment',
    edit_environment: 'Edit Environment',
    search_tips: 'Please enter keywords',
    edit: 'Edit',
    delete: 'Delete',
    environment_name: 'Environment Name',
    environment_config: 'Environment Config',
    environment_desc: 'Environment Desc',
    worker_groups: 'Worker Groups',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    delete_confirm: 'Delete?',
    environment_name_tips: 'Please enter your environment name',
    environment_config_tips: 'Please enter your environment config',
    environment_description_tips: 'Please enter your environment description',
    worker_group_tips: 'Please select worker group'
  },
  token: {
    create_token: 'Create Token',
    edit_token: 'Edit Token',
    search_tips: 'Please enter keywords',
    user: 'User',
    user_tips: 'Please select user',
    token: 'Token',
    token_tips: 'Please click to get token',
    expiration_time: 'Expiration Time',
    expiration_time_tips: 'Please select expiration time',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    delete: 'Delete',
    delete_confirm: 'Delete?'
  },
  user: {
    user_manage: 'User Manage',
    create_user: 'Create User',
    edit_user: 'Edit User',
    delete_user: 'Delete User',
    delete_confirm: 'Are you sure to delete?',
    delete_confirm_tip:
      'Deleting user is a dangerous operation，please be careful',
    project: 'Project',
    resource: 'Resource',
    file_resource: 'File Resource',
    udf_resource: 'UDF Resource',
    datasource: 'Datasource',
    udf: 'UDF Function',
    authorize_project: 'Project Authorize',
    authorize_resource: 'Resource Authorize',
    authorize_datasource: 'Datasource Authorize',
    authorize_udf: 'UDF Function Authorize',
    username_exists: 'The username already exists',
    username_tips: 'Please enter username',
    tenant_tips: 'Please select tenant',
    user_password: 'Password',
    user_password_tips:
      'Please enter a password containing letters and numbers with a length between 6 and 20',
    user_type: 'User Type',
    ordinary_user: 'Ordinary users',
    administrator: 'Administrator',
    tenant_code: 'Tenant',
    queue: 'Queue',
    queue_tips: 'Please select a queue',
    email: 'Email',
    email_empty_tips: 'Please enter email',
    emial_correct_tips: 'Please enter the correct email format',
    phone: 'Phone',
    phone_empty_tips: 'Please enter phone number',
    phone_correct_tips: 'Please enter the correct mobile phone format',
    state: 'State',
    state_enabled: 'Enabled',
    state_disabled: 'Disabled',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    delete: 'Delete',
    authorize: 'Authorize',
    save_error_msg: 'Failed to save, please retry',
    delete_error_msg: 'Failed to delete, please retry',
    auth_error_msg: 'Failed to authorize, please retry',
    auth_success_msg: 'Authorize succeeded',
    enable: 'Enable',
    disable: 'Disable',
    association_permission: 'Association Permission',
    view_permission: 'Check Role',
    reset_password: 'Reset Password',
    import_user: 'Import User',
    download_template: 'Download Template',
    alias: 'Alias',
    username: 'Username',
    roles_owned: 'Roles Owned',
    description: 'Description',
    association_role: 'Association Role',
    account_password: 'Account Password',
    reset_confirm: 'Reset Password?',
    all_role: 'All Roles',
    success: 'Success',
    import_success: 'Import User Success',
    reset_req_success:
      'The password reset request is successful, please check your email',
    reset_success: 'Password Reset Success',
    search_tips: 'Please enter keywords',
    alias_tips: 'Please enter alias',
    association_role_tips: 'Please association role',
    tenant_id_tips: 'Please select tenant',
    description_tips: 'Please enter description',
    check_error: 'User import data check error',
    user_account: 'User Account',
    user_name: 'User Name',
    system_admin: 'System Admin',
    project_role: 'project Role',
    none: 'None'
  },
  role: {
    role_name: 'Role Name',
    associated_users: 'Associated Number Of Users',
    create_time: 'Create Time',
    operation: 'Operation',
    check_authority: 'Check Role',
    rename: 'Rename',
    distribute_authority: 'Authority Of Distribution',
    delete: 'Delete',
    delete_confirm: 'Delete?',
    create_role: 'Create Role',
    name_tips: 'Please enter a role name',
    search_tips: 'Please input the keywords',
    functional_authority: 'Functional Authority',
    resource_authority: 'Resource Authority',
    name: 'Name',
    type: 'Type',
    current_role: 'Current Role',
    confirm: 'Confirm',
    cancel: 'Cancel',
    function_resource_empty_tips:
      'Please select a functional authority or a resource authority!',
    success: 'success',
    module: 'Module',
    page: 'Page',
    function: 'Function'
  },
  alarm_instance: {
    search_input_tips: 'Please input the keywords',
    alarm_instance_manage: 'Alarm instance manage',
    alarm_instance_name: 'Alarm instance name',
    alarm_instance_name_tips: 'Please enter alarm plugin instance name',
    alarm_plugin_name: 'Alarm plugin name',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit_alarm_instance: 'Edit Alarm Instance',
    delete: 'Delete',
    edit: 'Edit',
    delete_confirm: 'Delete?',
    confirm: 'Confirm',
    cancel: 'Cancel',
    submit: 'Submit',
    create_alarm_instance: 'Create Alarm Instance',
    select_plugin: 'Select plugin',
    select_plugin_tips: 'Select Alarm plugin',
    instance_parameter_exception: 'Instance parameter exception',
    WebHook: 'WebHook',
    webHook: 'WebHook',
    WarningType: 'Warning Type',
    IsEnableProxy: 'Enable Proxy',
    Proxy: 'Proxy',
    Port: 'Port',
    User: 'User',
    corpId: 'CorpId',
    secret: 'Secret',
    Secret: 'Secret',
    users: 'Users',
    userSendMsg: 'UserSendMsg',
    'agentId/chatId': 'AgentId or ChatId',
    showType: 'Show Type',
    receivers: 'Receivers',
    receiverCcs: 'ReceiverCcs',
    serverHost: 'SMTP Host',
    serverPort: 'SMTP Port',
    sender: 'Sender',
    enableSmtpAuth: 'SMTP Auth',
    Password: 'Password',
    starttlsEnable: 'SMTP STARTTLS Enable',
    sslEnable: 'SMTP SSL Enable',
    smtpSslTrust: 'SMTP SSL Trust',
    url: 'URL',
    requestType: 'Request Type',
    headerParams: 'Headers',
    bodyParams: 'Body',
    contentField: 'Content Field',
    Keyword: 'Keyword',
    userParams: 'User Params',
    path: 'Script Path',
    type: 'Type',
    sendType: 'Send Type',
    username: 'Username',
    botToken: 'Bot Token',
    chatId: 'Channel Chat Id',
    parseMode: 'Parse Mode',
    IntegrationKey: 'Integration Key',
    BotAccessToken: 'Bot Access Token',
    RoomId: 'Room Id',
    ToPersonId: 'To Person Id',
    ToPersonEmail: 'To Person Email',
    AtSomeoneInRoom: 'At Someone In Room',
    Destination: 'Destination',
    AtMobiles: 'At User Mobiles',
    AtUserIds: 'At User Ids',
    MsgType: 'Msg Type',
    // eslint-disable-next-line quotes
    IsAtAll: "{'@'}All"
  },
  k8s_namespace: {
    create_namespace: 'Create Namespace',
    edit_namespace: 'Edit Namespace',
    search_tips: 'Please enter keywords',
    k8s_namespace: 'K8S Namespace',
    k8s_namespace_tips: 'Please enter k8s namespace',
    k8s_cluster: 'K8S Cluster',
    k8s_cluster_tips: 'Please enter k8s cluster',
    owner: 'Owner',
    owner_tips: 'Please enter owner',
    tag: 'Tag',
    tag_tips: 'Please enter tag',
    limit_cpu: 'Limit CPU',
    limit_cpu_tips: 'Please enter limit CPU',
    limit_memory: 'Limit Memory',
    limit_memory_tips: 'Please enter limit memory',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    delete: 'Delete',
    delete_confirm: 'Delete?'
  },
  calendar: {
    calendar: 'Calendar',
    create: 'Create Calendar',
    edit_calendar: 'Edit Calendar',
    edit: 'Edit',
    delete: 'Delete',
    delete_confirm: 'Delete?',
    search_tips: 'Please enter keywords',
    name: 'Calendar Name',
    operation: 'Operation',
    cancel: 'Cancel',
    confirm: 'Confirm',
    type: 'Calendar Type',
    description: 'Calendar Description',
    name_tips: 'Please enter calendar name',
    description_tips: 'Please enter calendar description',
    import_calendar: 'Import calendar',
    export_calendar_template: 'Export Calendar Template',
    select_date: 'Select Date',
    select_date_tips: 'Please select date',
    no_execution: 'No Execution',
    execution: 'Execution',
    calender_config_rules: 'Date Format',
    upload_tips: 'Click or drag files to this area to upload.',
    import_success_tips: 'Date import successfully',
    import_failure_tips: 'Date import failure',
    file_wrong_format: 'The uploaded file is in the wrong format',
    date_wrong_format_part: 'Part of the date format is incorrect',
    date_wrong_format: 'Incorrect date format',
    import_empty_file: 'Import an empty sheet',
    import_partial_failure_tips: 'Partial date import failure'
  },
  card: {
    create_card: 'Create Card',
    edit_card: 'Edit Card',
    card_name: 'Card Name',
    calendar: 'Calendar',
    card_value: 'Card Value',
    flop_direction: 'Flop Direction',
    day_step: 'Day Step',
    hour_step: 'Hour Step',
    minute_step: 'Minute Step',
    output_format: 'Output Format',
    card_process_definition_name: 'Own Process Definition',
    card_user_name: 'Own User',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    edit_value: 'Edit Value',
    delete: 'Delete',
    forward: 'Forward',
    backward: 'Backward',
    day: 'Day',
    hour: 'Hour',
    minute: 'Minute',
    delete_confirm: 'Delete?',
    card_name_tips: 'Please input the card name',
    calendar_tips: 'Please select Calendar',
    card_value_tips: 'Please input the card value',
    output_format_tips: 'Please select output format for Calendar',
    day_step_tips: 'Please input day step',
    hour_step_tips: 'Please input hour step',
    minute_step_tips: 'Please input minute step',
    step_feedback: 'The step time cannot all be 0.',
    notification_strategy: 'Notification Strategy',
    alarm_group: 'Alarm Group',
    please_choose: 'Please Choose',
    description: 'Description',
    description_tips: 'Please enter card description'
  },
  label: {
    label: 'Label',
    create_label: 'Create Label',
    edit_label: 'Edit Label',
    edit: 'Edit',
    delete: 'Delete',
    delete_confirm: 'Delete?',
    search_tips: 'Please enter keywords',
    label_name: 'Label Name',
    label_color: 'Label Color',
    operation: 'Operation',
    cancel: 'Cancel',
    confirm: 'Confirm',
    label_description: 'Label Description',
    label_name_tips: 'Please enter label name',
    label_description_tips: 'Please enter label description',
    label_code: 'Label Code',
    label_code_tips: 'Please enter label code',
    label_color_tips: 'Please enter label color'
  },
  audit: {
    export: 'Export',
    export_excel: 'Export Excel',
    alias: 'Alias',
    username: 'Username',
    operate: 'Operate',
    operate_time: 'Operate Time',
    export_date: 'Export date',
    export_date_tips: 'Please select export date',
    export_tips:
      'The default export format is xlsx, and the export date range is allowed to export data of nearly three months.'
  },
  timing: {
    create_timing: 'Create Timing',
    edit_timing: 'Edit Timing',
    search_tips: 'Please enter keywords',
    timing_name: 'Timing Name',
    by_using_the_number_tip1: '',
    by_using_the_number_tip2:
      'workflows are known to be associated with this timing, and after successful editing, the associated workflows will be executed with the new timing!',
    crontab: 'Crontab',
    start_time: 'Start Time',
    end_time: 'End Time',
    by_using_the_number: 'By Using The Number',
    creator: 'Creator',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    delete: 'Delete',
    delete_confirm: 'Delete?',
    timing_name_tips: 'Please enter timing name',
    time_tips: 'Please choose start and stop time',
    start_and_stop_time: 'Start And Stop Time',
    timezone: 'Timezone',
    calender: 'Calender',
    timing: 'Timing',
    calender_tips: 'Please choose calender',
    next_five_execution_times: 'Next Five Execution Times',
    next_five_execution_times_tips:
      'No executable time based on current configuration',
    project_name: 'Project Name',
    workflow_name: 'Workflow Name',
    on_off_status: 'Online/Offline',
    online: 'Online',
    offline: 'Offline',
    times: 'Times',
    timing_used: 'The following workflows used this timing',
    cut_day_time: 'Cut Day Time'
  },
  packaged_deployment: {
    import: 'Import',
    export: 'Export',
    packaged_deployment: 'Packaged Deployment',
    name: 'Name',
    update_time: 'Update time',
    creator: 'Creator',
    error_messages: 'Error Messages',
    serial_number: 'No.',
    upload_tips: 'Click or drag files to this area to upload.',
    file_tips: ' The file is required',
    close: 'Close',
    workflow: 'workflow',
    export_empty_tips: 'Need to select a workflow',
    import_success: 'Imported successfully',
    not_support_type: 'File type is not supported'
  },
  license: {
    license_manage: 'License Manage',
    license_code: 'License authorization code',
    refresh_system_id: 'Refresh System ID',
    system_id: 'System ID',
    enter_license_code: 'Enter the License authorization code',
    license_days: 'Valid days of License',
    license_time: 'License validity range',
    contact: 'Contact customer service for a valid authorization code',
    refresh: 'Refresh',
    to: 'To',
    day: 'Days',
    remaining: 'Remaining',
    tips_success: 'The License authorization code is updated successfully',
    tips_jump: 'The login page is displayed after',
    seconds: 'seconds',
    close: 'Close',
    system_tips: 'System Tips',
    warning_tips: 'Your License has',
    warning_tips2:
      'Contact the administrator to update the License authorization code in time to prevent system usage from being affected',
    warning_tips3:
      'Your License does not exist or has expired. Please contact the administrator to update the License authorization code',
    warning_tips4: 'Authorization code update failed',
    version: 'Version',
    effective_date: 'Effective Date',
    node_information: 'Node Information',
    max_nodes_num: 'Max Number of Nodes'
  },
  baseline: {
    name: 'Baseline Name',
    type: 'Baseline Type',
    owner: 'Owner',
    project_name: 'Project Name',
    workflow_name: 'Workflow Name',
    alert_to: 'Alert Receiver',
    is_open: 'Is Open',
    timing: 'Timing',
    promise_time: 'Commitment Time',
    operation: 'Operation',
    edit: 'Edit',
    on: 'ON',
    off: 'OFF',
    delete: 'Delete',
    detail: 'Detail',
    create_baseline: 'Create Baseline',
    edit_baseline: 'Edit Baseline',
    status: 'Status',
    margin: 'Remaining Time',
    task_name: 'Task Name',
    task_type: 'Task Type',
    estimated_time: 'Estimated Time',
    support_task: 'Support Task',
    baseline_name_tips: 'Please enter baseline name',
    project_tips: 'Please select project',
    workflow_tips: 'Please select workflow',
    owner_tips: 'Please select owner',
    alert_tips: 'Please select Alert Group',
    promise_time_tips: 'Please enter commitment time',
    margin_tips: 'Please enter remaining time',
    minute: 'Minute',
    every_day: 'Every Day',
    alarm_margin: 'Remaining Time',
    return: 'Return',
    baseline_instance: 'Baseline Instance',
    baseline_instance_detail: 'Baseline Instance Detail',
    dealer: 'Dealer',
    baseline_task: 'Baseline Task',
    gantt: 'Gantt',
    baseline_promise: 'Baseline Commitment',
    base_info: 'Base Info',
    baseline_alarm: 'Baseline Alarm',
    baseline_deal: 'Baseline Deal',
    deal: 'Deal',
    daily_baseline: 'Daily Baseline',
    save: 'SAVE',
    dangerous: 'DANGEROUS',
    break_line: 'BREAK_BASELINE',
    all_status: 'ALL',
    workflow_instance_name: 'Workflow Instance Name'
  },
  system_param: {
    add: 'Add',
    search_input_tips: 'Please enter keywords',
    key: 'Key',
    value: 'Value',
    description: 'Description',
    operation: 'Operation',
    confirm: 'Confirm',
    cancel: 'Cancel',
    edit: 'Edit',
    delete: 'Delete',
    key_tips: 'Please enter Key',
    value_tips: 'Please enter Value',
    system_biz_date:
      'The day before the schedule time of the daily scheduling instance, the format is yyyyMMdd',
    system_biz_curdate:
      'The schedule time of the daily scheduling instance, the format is yyyyMMdd',
    system_datetime:
      'The schedule time of the daily scheduling instance, the format is yyyyMMddHHmmss',
    system_project_code: 'Project ID',
    system_project_name: 'Project Name',
    system_workflow_code: 'Workflow ID',
    system_workflow_name: 'Workflow Name',
    system_task_code: 'Task ID',
    system_task_name: 'Task Name',
    yyyy_mm_dd_hh_mm_ss:
      'Time format $[yyyyMMddHHmmss] can be decomposed and combined arbitrarily, such as: $[yyyyMMdd], $[HHmmss], $[yyyy-MM-dd], etc.',
    suffix_n_year: 'Next N years',
    prefix_n_year: 'N years before',
    suffix_n_month: 'Next N months',
    prefix_n_month: 'N months before',
    suffix_n_week: 'Next N weeks',
    prefix_n_week: 'First N weeks',
    suffix_n_day: 'Next N days',
    prefix_n_day: 'N days before',
    suffix_n_hour: 'Next N hours',
    prefix_n_hour: 'First N hours',
    suffix_n_minute: 'Next N minutes',
    prefix_n_minute: 'First N minutes',
    var: 'Variable declaration',
    var_base_time: 'Replace custom base time yyyyMMdd',
    var_format:
      'Replace custom time format：yyyy-MM-dd HH:mm:ss、yyyy-MM-dd HH:mm、yyyy-MM-dd、yyyy-MM、yyyyMM',
    var_n:
      'The n of the natural day/trading day function is set to 0,; Range function, set the letter n to mean weekly/monthly/quarterly; When n is an integer, -N means the first n weeks/months/quarters, 0 means the current week/months/quarters, and n means the last n weeks/months/quarters;',
    var_m:
      'When the begin function is used, the trading day/natural day m is all integers, and other functions m are positive integers. For example, -M means the first m day, 0 means the current day, and m means the last m day; When the range function is used, m is an integer not equal to 0 (the m outside parentheses is a fixed identifier). For example, -M means the last m days of the week/month/quarter, 0 means the result is empty, and m means the first m days of the week/month/quarter;',
    var_calendar: 'Transaction calendar name',
    natural_begin_m:
      'Natural day function calculation, which is used to calculate the m-th day before/after the benchmark time. Example: $ [natural _ begin _ m (20220628, yyyy-mm-DD, 0,0)] means the current natural day: 2022-06-28 $ [natural _ begin _ m (20220628). 1)] means the next natural day: June 29, 2022 108 $ [natural _ begin _ m (June 28, 2022, yyyy-mm-DD, 0,-1)] means the last natural day: June 27, 2022.',
    basic_param: 'Basic built-in parameters',
    derive_param: 'Derived built-in parameter',
    calendar_function_param: 'Calendar function value',
    param_key_title_tips:
      // eslint-disable-next-line quotes
      "Parameter variables can be directly referenced by {'$'} {'{'}parameter key{'}'} in workflow task scripts.",
    param_value_title_tips:
      // eslint-disable-next-line quotes
      "Please fill in the string directly for constants, and add the format {'$'} {'{'}{'}'} for function variables.",
    basic_param_tips:
      'Basic parameter values built into the system, which are used after being given parameter key names.',
    derive_param_tips:
      'Derived parameter values built into the system, which are used after replacing variables and giving parameter key names.',
    calendar_function_param_tips:
      'The calendar function built into the system replaces the variable and gives the parameter key name to use.',
    handle_tips: 'Parameter key and value cannot be empty.',
    workflow_name: 'Workflow Name',
    task_name: 'Task Name',
    own_project: 'Own Project',
    executor: 'Executor',
    begin_time: 'Begin Time',
    end_time: 'End Time',
    updater: 'Updater',
    allow_update: 'Allow Task Update',
    update_time: 'Update Time'
  }
}
