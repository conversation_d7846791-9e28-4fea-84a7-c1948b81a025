/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.list-container {
  width: 250px;

  dl {
    overflow: auto;
    height: calc(100vh - 160px);

    dd {
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 15px 0;
      border-bottom: 1px solid #ddd;
    }

    .dd-active {
      color: #1890ff;
    }
  }

}
.adjust-th-height {
    height: 48px;
}

.table-container-margin {
  margin: 0 12px 0 36px;
}

:global .row-error {
  color: #ff4d4f;
}
