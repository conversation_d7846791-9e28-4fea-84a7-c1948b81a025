/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  id: 'Id',
  datasource: 'DataSource',
  create_datasource: 'Create DataSource',
  choose_datasource_type: 'Choose DataSource Type',
  search: 'Search',
  search_input_tips: 'Please input the keywords',
  select: 'Select',
  datasource_name: 'Datasource Name',
  datasource_name_tips: 'Please enter datasource name',
  datasource_user_name: 'Owner',
  datasource_type: 'Datasource Type',
  datasource_parameter: 'Datasource Parameter',
  description: 'Description',
  description_tips: 'Please enter description',
  create_time: 'Create Time',
  update_time: 'Update Time',
  operation: 'Operation',
  click_to_view: 'Click to view',
  delete: 'Delete',
  delete_confirm: 'Are you sure delete ?',
  confirm: 'Confirm',
  cancel: 'Cancel',
  create: 'Create',
  edit: 'Edit',
  edit_datasource: 'Edit DataSource',
  success: 'Success',
  test_connect: 'Test Connect',
  test_connect_success: 'Test Connect Success',
  ip: 'IP',
  ip_tips: 'Please enter IP',
  port: 'Port',
  port_tips: 'Please enter port',
  database_name: 'Database Name',
  database_name_tips: 'Please enter database name',
  oracle_connect_type: 'ServiceName or SID',
  oracle_connect_type_tips: 'Please select serviceName or SID',
  oracle_service_name: 'ServiceName',
  oracle_sid: 'SID',
  jdbc_connect_parameters: 'jdbc connect parameters',
  principal_tips: 'Please enter Principal',
  authentication_type: 'Authentication Type',
  dm_server_authentication: 'DM Server Authentication',
  ldap_authentication: 'LDAP Authentication',
  operating_system_authentication: 'Operating System Authentication',
  kerberos_authentication: 'Kerberos Authentication',
  krb5_conf_tips:
    'Please enter the kerberos authentication parameter java.security.krb5.conf',
  keytab_username_tips:
    'Please enter the kerberos authentication parameter login.user.keytab.username',
  keytab_path_tips:
    'Please enter the kerberos authentication parameter login.user.keytab.path',
  format_tips: 'Please enter format',
  connection_parameter: 'connection parameter',
  user_name: 'User Name',
  user_name_tips: 'Please enter your username',
  user_name_tips1: 'please enter “/”+username,for example:/username',
  user_name_tips2: 'please enter “//”+username,for example://username',
  user_name_tips3: 'please enter “///”+username,for example:///username',
  user_password: 'Password',
  user_password_tips: 'Please enter your password',
  jdbc_format_tips: 'jdbc connection parameters is not a correct JSON format',
  all: 'All',
  warning: 'Warning',
  close_confirm_tips:
    'This operation will lose the source currently being created',
  database: 'Database',
  file: 'File',
  no_structured: 'NoSQLs',
  storage: 'Storage',
  data_analysis: 'Data Analysis',
  remote_connection: 'Remote Connection',
  fake_connection: 'Fake Connection'
}
