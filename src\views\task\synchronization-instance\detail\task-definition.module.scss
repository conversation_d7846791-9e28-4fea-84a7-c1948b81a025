/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.right-panel {
  max-height: calc(100vh - 235px);
  overflow: auto;

  h4 {
    margin: 0;
  }
}

.left-panel {
  min-height: calc(100vh - 235px);

  .workflow-dag {
    display: flex;
    height: calc(100vh - 275px);

    .container {
      height: 100%;
      width: 100%;
    }

    .dag-container {
      height: 100%;
      width: 100%;
    }

    .minimap {
      position: absolute;
      right: 50px;
      bottom: 45px;
      border: dashed 1px #e4e4e4;
      z-index: 9000;
    }
  }
}
