/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

 .errorMessageContainer {
   max-height: 400px;
   overflow-y: auto;
   white-space: pre-wrap; /* Preserver formatting */
   background-color: #f4f4f4;
   padding: 10px;
   border: 1px solid #ccc;
   border-radius: 4px;
   font-family: monospace;
   text-align: left;
   margin: 0 auto;
   width: 1000px;
   max-width: 100%;
 }