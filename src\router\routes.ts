/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import utils from '@/utils'
import tasks from '@/router/tasks'
import userManage from '@/router/user-manage'
import datasource from '@/router/datasource'
import virtualTables from '@/router/virtual-tables'
import type { RouteRecordRaw } from 'vue-router'
import type { Component } from 'vue'

const modules = import.meta.glob('/src/views/**/**.tsx')
const components: { [key: string]: Component } = utils.mapping(modules)

const basePage: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: { name: 'login' }
  },
  tasks,
  userManage,
  datasource,
  virtualTables
]

const loginPage: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'login',
    component: components['login']
  },
  {
    path: '/setting',
    redirect: { name: 'setting' },
    component: () => import('@/layouts/dashboard'),
    children: [
      {
        path: '/setting',
        name: 'setting',
        component: components['setting'],
        meta: {
          title: 'setting'
        }
      }
    ]
  }
]

const routes: RouteRecordRaw[] = [...basePage, ...loginPage]

export default routes
