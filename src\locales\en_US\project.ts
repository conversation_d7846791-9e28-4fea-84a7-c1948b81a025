/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  list: {
    create_project: 'Create Project',
    edit_project: 'Edit Project',
    project_list: 'Project List',
    project_tips: 'Please enter your project',
    description_tips: 'Please enter your description',
    username_tips: 'Please enter your username',
    project_name: 'Project Name',
    project_description: 'Project Description',
    owned_users: 'Owned Users',
    workflow_define_count: 'Workflow Define Count',
    process_instance_running_count: 'Process Instance Running Count',
    description: 'Description',
    create_time: 'Create Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    delete: 'Delete',
    confirm: 'Confirm',
    cancel: 'Cancel',
    delete_confirm: 'Delete?',
    member_manage: 'Member Manage',
    data_columns: 'Data columns'
  },
  overview: {
    task_state_statistics: 'Task State Statistics',
    process_state_statistics: 'Process State Statistics',
    process_definition_statistics: 'Process Definition Statistics',
    number: 'Number',
    state: 'State',
    submitted_success: 'SUBMITTED_SUCCESS',
    running_execution: 'RUNNING_EXECUTION',
    ready_pause: 'READY_PAUSE',
    pause: 'PAUSE',
    ready_stop: 'READY_STOP',
    stop: 'STOP',
    failure: 'FAILURE',
    success: 'SUCCESS',
    need_fault_tolerance: 'NEED_FAULT_TOLERANCE',
    kill: 'KILL',
    waiting_thread: 'WAITING_THREAD',
    waiting_depend: 'WAITING_DEPEND',
    delay_execution: 'DELAY_EXECUTION',
    forced_success: 'FORCED_SUCCESS',
    serial_wait: 'SERIAL_WAIT',
    dispatch: 'DISPATCH',
    ready_block: 'READY_BLOCK',
    block: 'BLOCK',
    pause_by_isolation: 'PAUSE_BY_ISOLATION',
    kill_by_isolation: 'KILL_BY_ISOLATION',
    pause_by_coronation: 'PAUSE_BY_CORONATION',
    forbidden_by_coronation: 'FORBIDDEN_BY_CORONATION'
  },
  workflow: {
    pause_recovery: 'Pause Recovery',
    pause_recovery_confirm: 'Pause Recovery?',
    suspend: 'Suspend',
    suspend_confirm: 'Suspend?',
    stop_recovery: 'Stop Recovery',
    stop_recovery_confirm: 'Stop Recovery?',
    copy_judge_tips:
      'In the current workflow, a node with brand name $value is referenced.',
    workflow_relation: 'Workflow Relation',
    create_workflow: 'Create Workflow',
    select_project: 'Choose a project',
    choose_project: 'Designated project',
    import_workflow: 'Import Workflow',
    export_workflow: 'Export Workflow',
    workflow_name: 'Workflow Name',
    workflow_instance_name: 'Workflow Instance Name',
    current_selection: 'Current Selection',
    online: 'Online',
    offline: 'Offline',
    refresh: 'Refresh',
    show_hide_label: 'Show / Hide Label',
    schedule_start_time: 'Schedule Start Time',
    schedule_end_time: 'Schedule End Time',
    crontab_expression: 'Crontab',
    workflow_publish_status: 'Workflow Publish Status',
    workflow_definition: 'Workflow Definition',
    workflow_instance: 'Workflow Instance',
    status: 'Status',
    create_time: 'Create Time',
    update_time: 'Update Time',
    description: 'Description',
    create_user: 'Create User',
    modify_user: 'Modify User',
    operation: 'Operation',
    edit: 'Edit',
    start: 'Start',
    timing: 'Timing',
    up_line: 'Online',
    down_line: 'Offline',
    copy_workflow: 'Copy Workflow',
    delete: 'Delete',
    tree_view: 'Tree View',
    tree_limit: 'Limit Size',
    export: 'Export',
    batch_copy: 'Copy',
    version_info: 'Version Info',
    version: 'Version',
    undo: 'Undo',
    redo: 'Redo',
    file_upload: 'File Upload',
    upload_file: 'Upload File',
    import_type: 'Import Type',
    export_type: 'Export Type',
    error_info: 'The template format is wrong, please confirm and upload again',
    upload: 'Upload',
    file_name: 'File Name',
    success: 'Success',
    parameter_configuration: 'Parameter Configuration',
    set_parameters_before_timing: 'Set parameters before timing',
    failure_strategy: 'Failure Strategy',
    node_execution: 'Node Execution',
    backward_execution: 'Backward execution',
    forward_execution: 'Forward execution',
    current_node_execution: 'Execute only the current node',
    notification_strategy: 'Notification Strategy',
    workflow_priority: 'Workflow Priority',
    worker_group: 'Worker Group',
    environment_name: 'Environment Name',
    alarm_group: 'Alarm Group',
    calendar: 'Calendar',
    cut_day_time: 'Cut Day Time',
    card: 'Card',
    card_value: 'Card Value',
    complement_data: 'Complement Data',
    startup_parameter: 'Startup Parameter',
    whether_dry_run: 'Whether Dry-Run',
    continue: 'Continue',
    end: 'End',
    none_send: 'None',
    success_send: 'Success',
    failure_send: 'Failure',
    all_send: 'All',
    whether_complement_data: 'Whether it is a complement process?',
    data_date: 'Data date',
    select_date: 'Select Date',
    enter_date: 'Enter Date',
    data_date_tips:
      'The format is yyyy-MM-dd HH:mm:ss with multiple comma splits',
    data_date_limit: 'Enter more than 100 dates',
    mode_of_execution: 'Mode of execution',
    serial_execution: 'Serial execution',
    parallel_execution: 'Parallel execution',
    parallelism: 'Parallelism',
    custom_parallelism: 'Custom Parallelism',
    please_enter_parallelism: 'Please enter Parallelism',
    please_choose: 'Please Choose',
    start_time: 'Start Time',
    end_time: 'End Time',
    delete_confirm: 'Delete?',
    rerun_confirm: 'Rerun?',
    stop_confirm: 'Stop?',
    online_confirm: 'Online?',
    offline_confirm: 'Offline?',
    enter_name_tips: 'Please enter name',
    uploaded_file_tips: 'The uploaded file cannot be empty',
    switch_version: 'Switch To This Version',
    confirm_switch_version: 'Confirm Switch To This Version?',
    current_version: 'Current Version',
    run_type: 'Run Type',
    scheduling_time: 'Scheduling Time',
    duration: 'Duration',
    run_times: 'Run Times',
    fault_tolerant_sign: 'Fault-tolerant Sign',
    dry_run_flag: 'Dry-run Flag',
    executor: 'Executor',
    host: 'Host',
    start_process: 'Start Process',
    start_from_state_clean_tasks: 'Start from state clean tasks',
    execute_from_the_current_node: 'Execute from the current node',
    recover_tolerance_fault_process: 'Recover tolerance fault process',
    resume_the_suspension_process: 'Resume the suspension process',
    execute_from_the_failed_nodes: 'Execute from the failed nodes',
    scheduling_execution: 'Scheduling execution',
    rerun: 'Rerun',
    stop: 'Stop',
    pause: 'Pause',
    recovery_waiting_thread: 'Recovery waiting thread',
    recover_serial_wait: 'Recover serial wait',
    recovery_suspend: 'Recovery Suspend',
    dependent_chain_rerun: 'Dependent Chain Rerun',
    dependent_chain_recovery: 'Dependent Chain Recovery',
    failed_to_retry: 'Failed to retry',
    gantt: 'Gantt',
    name: 'Name',
    all_status: 'AllStatus',
    submit_success: 'Submitted successfully',
    running: 'Running',
    ready_to_pause: 'Ready to pause',
    ready_to_stop: 'Ready to stop',
    failed: 'Failed',
    need_fault_tolerance: 'Need fault tolerance',
    kill: 'Kill',
    waiting_for_thread: 'Waiting for thread',
    waiting_for_dependence: 'Waiting for dependence',
    waiting_for_dependency_to_complete: 'Waiting for dependency to complete',
    delay_execution: 'Delay execution',
    forced_success: 'Forced success',
    serial_wait: 'Serial wait',
    dispatch: 'Dispatch',
    executing: 'Executing',
    startup_type: 'Startup Type',
    complement_range: 'Complement Range',
    parameters_variables: 'Parameters variables',
    global_parameters: 'Global parameters',
    local_parameters: 'Local parameters',
    type: 'Type',
    retry_count: 'Retry Count',
    submit_time: 'Submit Time',
    refresh_status_succeeded: 'Refresh status succeeded',
    view_log: 'View log',
    update_log_success: 'Update log success',
    no_more_log: 'No more logs',
    no_log: 'No log',
    loading_log: 'Loading Log...',
    close: 'Close',
    download_log: 'Download Log',
    refresh_log: 'Refresh Log',
    enter_full_screen: 'Enter full screen',
    cancel_full_screen: 'Cancel full screen',
    task_state: 'Task status',
    mode_of_dependent: 'Mode of dependent',
    open: 'Open',
    project_name_required: 'Project name is required',
    related_items: 'Related items',
    project_name: 'Project Name',
    project_tips: 'Please select project name',
    workflow_relation_no_data_result_title:
      'Can not find any relations of workflows.',
    workflow_relation_no_data_result_desc:
      'There is not any workflows. Please create a workflow, and then visit this page again.',
    timing_tips: 'The current workflow lacks timing',
    label: 'Label',
    estimated_end_time: 'Estimated End Time',
    download_template: 'Download Template',
    export_tips:
      'Currently, Shell, SQL, Procedure, Dependent, Trigger and Data Quality tasks can be exported',
    timing_name: 'Timing Name',
    next_run: 'Next Running Time',
    coronation: 'Coronation',
    cancle_coronation: 'Cancel the coronation',
    coronation_confirm: 'Confirm coronation?',
    cancle_coronation_confirm: 'Sure to cancel the coronation?',
    isolation: 'Isolation',
    cancle_isolation__confirm: 'Sure to cancel the isolation?',
    cancle_isolation: 'Cancel the isolation',
    isolation_confirm: 'Confirm isolation?',
    debug_tips: 'Save workflow before debug',
    impact_anaysis: 'Impact analysis',
    natural: 'Calendar day',
    scheduler_calendar: 'Scheduling calendars',
    card_calendar: 'Card calendars',
    custom_calendar: 'Custom calendars',
    count: 'count',
    choose_timing: 'Please select timing',
    instance: 'Workflow Instance',
    submitter: 'Submitter',
    today: 'Today',
    last_day: 'Yesterday',
    last_three_day: 'The last 3 days',
    last_week: 'The last 7 days',
    last_month: 'The last 30 days',
    local_import: 'Local Import',
    workflow_update_list: 'Workflow Update List',
    update: 'Update',
    create: 'Create',
    last_update_user: 'Last Update User',
    last_update_time: 'Last Update Time',
    dependent_chain_title:
      'The following workflow are about to start dependency chain cleanup and rerun',
    dependent_chain_condition_title:
      'The following workflow do not meet the execution conditions'
  },
  task: {
    cancel_full_screen: 'Cancel full screen',
    enter_full_screen: 'Enter full screen',
    current_task_settings: 'Current task settings',
    online: 'Online',
    offline: 'Offline',
    task_name: 'Task Name',
    task_type: 'Task Type',
    create_task: 'Create Task',
    workflow_instance: 'Workflow Instance',
    workflow_name: 'Workflow Name',
    workflow_name_tips: 'Please select workflow name',
    workflow_state: 'Workflow State',
    version: 'Version',
    current_version: 'Current Version',
    switch_version: 'Switch To This Version',
    confirm_switch_version: 'Confirm Switch To This Version?',
    description: 'Description',
    move: 'Move',
    upstream_tasks: 'Upstream Tasks',
    executor: 'Executor',
    node_type: 'Node Type',
    state: 'State',
    submit_time: 'Submit Time',
    start_time: 'Start Time',
    create_time: 'Create Time',
    update_time: 'Update Time',
    end_time: 'End Time',
    duration: 'Duration',
    retry_count: 'Retry Count',
    dry_run_flag: 'Dry Run Flag',
    host: 'Host',
    operation: 'Operation',
    edit: 'Edit',
    delete: 'Delete',
    delete_confirm: 'Delete?',
    clean_state: 'Clear and run again',
    forced_success: 'Forced Success',
    view_log: 'View Log',
    download_log: 'Download Log',
    refresh: 'Refresh',
    run_state: 'Run State',
    workflow_instance_name: 'Workflow Instance Name',
    cancel_coronation: 'Cancel Coronation',
    import_coronation_task: 'Import Coronation Task',
    download_template: 'Download Template',
    cancel_confirm: 'Cancel?',
    task_coronation: 'Task Coronation',
    upload_file: 'Upload File',
    local_import: 'Local Import',
    task_coronation_list: 'Task Coronation List',
    associate_upstream: 'Associate Upstream',
    next_step: 'Next Step',
    prev_step: 'Prev Step',
    confirm: 'Confirm',
    error_info: 'The template format is wrong, please confirm and upload again',
    more: 'More',
    success: 'Success',
    flag: 'Flag',
    newest: 'Newest',
    history: 'History',
    confirm_operation: 'Confirm',
    detail: 'Detail',
    return: 'Return',
    cancel: 'Cancel',
    dependent_chain_title:
      'The following tasks are about to start dependency chain cleanup and rerun',
    dependent_chain_condition_title:
      'The following tasks do not meet the execution conditions',
    dependent_chain_condition_tip1:
      'An executing workflow instance is not executable',
    dependent_chain_condition_tip2:
      'The task cannot be executed if it does not meet the execution conditions (the predecessor task of the task is suspended or failed)',
    dependent_chain_condition_tip3:
      'Non-executable without operation permission',
    batch_confirm: 'Batch Confirm'
  },
  dag: {
    create: 'Create Workflow',
    start: 'Start',
    search: 'Search',
    download_png: 'Download PNG',
    import_task: 'Import task',
    download_template: 'Download task template',
    fullscreen_open: 'Open Fullscreen',
    fullscreen_close: 'Close Fullscreen',
    save: 'Save',
    close: 'Close',
    format: 'Format',
    refresh_dag_status: 'Refresh DAG status',
    layout_type: 'Layout Type',
    grid_layout: 'Grid',
    dagre_layout: 'Dagre',
    rows: 'Rows',
    cols: 'Cols',
    copy_success: 'Copy Success',
    workflow_name: 'Workflow Name',
    description: 'Description',
    tenant: 'Tenant',
    timeout_alert: 'Timeout Alert',
    process_execute_type: 'Process execute type',
    parallel: 'parallel',
    serial_wait: 'Serial wait',
    // serial_discard: 'Serial discard',
    serial_priority: 'Serial priority',
    recover_serial_wait: 'Recover serial wait',
    global_variables: 'Global Variables',
    basic_info: 'Basic Information',
    minute: 'Minute',
    key: 'Key',
    value: 'Value',
    success: 'Success',
    delete_cell: 'Delete selected edges and nodes',
    online_directly: 'Whether to go online the process definition',
    update_directly: 'Whether to update the process definition',
    dag_name_empty: 'Workflow name cannot be empty',
    project_empty: 'Please select project',
    positive_integer: 'Please enter a positive integer greater than 0',
    prop_empty: 'prop is empty',
    prop_repeat: 'prop is repeat',
    node_not_created: 'Failed to save node not created',
    copy_name: 'Copy Name',
    view_variables: 'View Variables',
    startup_parameter: 'Startup Parameter',
    online: 'Online',
    label: 'Label',
    label_length_tips: 'The maximum number of labels is 3',
    unsave_tips:
      'Please save the workflow first, and then execute the run operation.',
    cancel: 'Cancel',
    force_close: 'Force Close',
    close_tip: 'Close Tip',
    close_content_tip:
      'The current workflow has been modified and has not been saved. If it is forced to close, the edited content will be lost.',
    more: 'More',
    run: 'Run',
    stop: 'Stop',
    import: 'Import',
    code_import: 'File import',
    resource: 'Resource',
    debug_tips: 'Getting the log, please wait...',
    setting: 'Setting',
    form_tips: 'Error in required information of node',
    save_and_debug: 'Save Workflow and debug',
    debug: 'Debug',
    import_alert:
      'When the script content is updated, the referenced file content is also updated.',
    select_file: 'Select a file'
  },
  node: {
    return_back: 'Return',
    current_node_settings: 'Current node settings',
    instructions: 'Instructions',
    view_history: 'View history',
    view_log: 'View log',
    enter_this_child_node: 'Enter this child node',
    name: 'Node Name',
    task_name: 'Task Name',
    task_name_tips: 'Please select a task (required)',
    name_tips: 'Please enter name (required)',
    task_type: 'Task Type',
    task_type_tips: 'Please select a task type (required)',
    workflow_name: 'Workflow Name',
    workflow_name_tips: 'Please select a workflow (required)',
    child_node: 'Child Node',
    child_node_tips: 'Please select a child node (required)',
    run_flag: 'Run flag',
    normal: 'Normal',
    prohibition_execution: 'Prohibition execution',
    description: 'Description',
    description_tips: 'Please enter description',
    task_priority: 'Task priority',
    worker_group: 'Worker group',
    worker_group_tips:
      'The Worker group no longer exists, please select the correct Worker group!',
    environment_name: 'Environment Name',
    task_group_name: 'Task group name',
    task_group_queue_priority: 'Priority',
    number_of_failed_retries: 'Number of failed retries',
    times: 'Times',
    failed_retry_interval: 'Failed retry interval',
    minute: 'Minute',
    second: 'Second',
    delay_execution_time: 'Delay execution time',
    state: 'State',
    branch_flow: 'Branch flow',
    cancel: 'Cancel',
    loading: 'Loading...',
    confirm: 'Confirm',
    success: 'Success',
    failed: 'Failed',
    backfill_tips:
      'The newly created sub-Process has not yet been executed and cannot enter the sub-Process',
    task_instance_tips:
      'The task has not been executed and cannot enter the sub-Process',
    branch_tips:
      'Cannot select the same node for successful branch flow and failed branch flow',
    timeout_alarm: 'Timeout alarm',
    timeout_strategy: 'Timeout strategy',
    timeout_strategy_tips: 'Timeout strategy must be selected',
    timeout_failure: 'Timeout failure',
    timeout_period: 'Timeout period',
    timeout_period_tips: 'Timeout must be a positive integer',
    script: 'Script',
    script_tips: 'Please enter script(required)',
    resources: 'Resources',
    resources_tips: 'Please select resources',
    non_resources_tips: 'Please delete all non-existent resources',
    useless_resources_tips: 'Unauthorized or deleted resources',
    custom_parameters: 'Custom Parameters',
    copy_success: 'Copy success',
    copy_failed: 'The browser does not support automatic copying',
    prop_tips: 'prop(required)',
    prop_repeat: 'prop is repeat',
    value_tips: 'value(optional)',
    value_required_tips: 'value(required)',
    pre_tasks: 'Pre tasks',
    program_type: 'Program Type',
    main_class: 'Main Class',
    main_class_tips: 'Please enter main class',
    main_package: 'Main Package',
    main_package_tips: 'Please enter main package',
    deploy_mode: 'Deploy Mode',
    app_name: 'App Name',
    app_name_tips: 'Please enter app name(optional)',
    driver_cores: 'Driver Cores',
    driver_cores_tips: 'Please enter Driver cores',
    driver_memory: 'Driver Memory',
    driver_memory_tips: 'Please enter Driver memory',
    executor_number: 'Executor Number',
    executor_number_tips: 'Please enter Executor number',
    executor_memory: 'Executor Memory',
    executor_memory_tips: 'Please enter Executor memory',
    executor_cores: 'Executor Cores',
    executor_cores_tips: 'Please enter Executor cores',
    main_arguments: 'Main Arguments',
    main_arguments_tips: 'Please enter main arguments',
    option_parameters: 'Option Parameters',
    option_parameters_tips: 'Please enter option parameters',
    positive_integer_tips: 'should be a positive integer',
    flink_version: 'Flink Version',
    job_manager_memory: 'JobManager Memory',
    job_manager_memory_tips: 'Please enter JobManager memory',
    task_manager_memory: 'TaskManager Memory',
    task_manager_memory_tips: 'Please enter TaskManager memory',
    slot_number: 'Slot Number',
    slot_number_tips: 'Please enter Slot number',
    parallelism: 'Parallelism',
    custom_parallelism: 'Configure parallelism',
    parallelism_tips: 'Please enter Parallelism',
    parallelism_number_tips: 'Parallelism number should be positive integer',
    parallelism_complement_tips:
      'If there are a large number of tasks requiring complement, you can use the custom parallelism to ' +
      'set the complement task thread to a reasonable value to avoid too large impact on the server.',
    task_manager_number: 'TaskManager Number',
    task_manager_number_tips: 'Please enter TaskManager number',
    http_url: 'Http Url',
    http_url_tips: 'Please Enter Http Url',
    http_url_validator: 'The request address must contain HTTP or HTTPS',
    http_method: 'Http Method',
    http_parameters: 'Http Parameters',
    http_check_condition: 'Http Check Condition',
    http_condition: 'Http Condition',
    http_condition_tips: 'Please Enter Http Condition',
    timeout_settings: 'Timeout Settings',
    connect_timeout: 'Connect Timeout',
    ms: 'ms',
    socket_timeout: 'Socket Timeout',
    status_code_default: 'Default response code 200',
    status_code_custom: 'Custom response code',
    body_contains: 'Content includes',
    body_not_contains: 'Content does not contain',
    body_check_custom: 'Content check',
    http_parameters_position: 'Http Parameters Position',
    target_task_name: 'Target Task Name',
    target_task_name_tips: 'Please enter the Pigeon task name(required)',
    datasource_type: 'Datasource types',
    datasource_instances: 'Datasource instances',
    datasource_require_tips: 'Datasource is required',
    sql_type: 'SQL Type',
    sql_type_query: 'Query',
    sql_type_non_query: 'Non Query',
    sql_statement: 'SQL Statement',
    pre_sql_statement: 'Pre SQL Statement',
    post_sql_statement: 'Post SQL Statement',
    sql_input_placeholder: 'Please enter non-query sql.',
    sql_empty_tips: 'The sql can not be empty.',
    procedure_method: 'SQL Statement',
    procedure_method_tips: 'Please enter the procedure script',
    procedure_method_snippet:
      '--Please enter the procedure script \n\n--call procedure:call <procedure-name>[(<arg1>,<arg2>, ...)]\n\n--call function:?= call <procedure-name>[(<arg1>,<arg2>, ...)]',
    start: 'Start',
    edit: 'Edit',
    copy: 'Copy',
    delete: 'Delete',
    custom_job: 'Custom Job',
    custom_script: 'Custom Script',
    sqoop_job_name: 'Job Name',
    sqoop_job_name_tips: 'Please enter Job Name(required)',
    direct: 'Direct',
    hadoop_custom_params: 'Hadoop Params',
    sqoop_advanced_parameters: 'Sqoop Advanced Parameters',
    data_source: 'Data Source',
    type: 'Type',
    datasource: 'Datasource',
    datasource_tips: 'Please select the datasource',
    model_type: 'ModelType',
    form: 'Form',
    table: 'Table',
    table_tips: 'Please enter Mysql Table(required)',
    column_type: 'ColumnType',
    all_columns: 'All Columns',
    some_columns: 'Some Columns',
    column: 'Column',
    column_tips: 'Please enter Columns (Comma separated)',
    database: 'Database',
    database_tips: 'Please enter Hive Database(required)',
    zeppelin_note_id: 'zeppelinNoteId',
    zeppelin_note_id_tips: 'Please enter the note id of your zeppelin note',
    zeppelin_paragraph_id: 'zeppelinParagraphId',
    zeppelin_paragraph_id_tips:
      'Please enter the paragraph id of your zeppelin paragraph',
    zeppelin_parameters: 'parameters',
    zeppelin_parameters_tips:
      'Please enter the parameters for zeppelin dynamic form',
    zeppelin_rest_endpoint: 'zeppelinRestEndpoint',
    zeppelin_rest_endpoint_tips:
      'Please enter the rest endpoint of your Zeppelin server',
    zeppelin_production_note_directory:
      'Directory for cloned zeppelin note in production mode',
    zeppelin_production_note_directory_tips:
      'Please enter the production note directory to enable production mode',
    hive_table_tips: 'Please enter Hive Table(required)',
    hive_partition_keys: 'Hive partition Keys',
    hive_partition_keys_tips: 'Please enter Hive Partition Keys',
    hive_partition_values: 'Hive partition Values',
    hive_partition_values_tips: 'Please enter Hive Partition Values',
    jupyter_conda_env_name: 'condaEnvName',
    jupyter_conda_env_name_tips:
      'Please enter the conda environment name of papermill',
    jupyter_input_note_path: 'inputNotePath',
    jupyter_input_note_path_tips: 'Please enter the input jupyter note path',
    jupyter_output_note_path: 'outputNotePath',
    jupyter_output_note_path_tips: 'Please enter the output jupyter note path',
    jupyter_parameters: 'parameters',
    jupyter_parameters_tips:
      'Please enter the parameters for jupyter parameterization',
    jupyter_kernel: 'kernel',
    jupyter_kernel_tips: 'Please enter the jupyter kernel name',
    jupyter_engine: 'engine',
    jupyter_engine_tips: 'Please enter the engine name',
    jupyter_execution_timeout: 'executionTimeout',
    jupyter_execution_timeout_tips:
      'Please enter the execution timeout for each jupyter note cell',
    jupyter_start_timeout: 'startTimeout',
    jupyter_start_timeout_tips:
      'Please enter the start timeout for jupyter kernel',
    jupyter_others: 'others',
    jupyter_others_tips:
      'Please enter the other options you need for papermill',
    mlflow_algorithm: 'Algorithm',
    mlflow_algorithm_tips: 'svm',
    mlflow_params: 'Parameters',
    mlflow_params_tips: ' ',
    mlflow_searchParams: 'Parameter Search Space',
    mlflow_searchParams_tips: ' ',
    mlflow_isSearchParams: 'Search Parameters',
    mlflow_dataPath: 'Data Path',
    mlflow_dataPath_tips:
      ' The absolute path of the file or folder. Ends with .csv for file or contain train.csv and test.csv for folder',
    mlflow_dataPath_error_tips: ' data data can not be empty ',
    mlflow_experimentName: 'Experiment Name',
    mlflow_experimentName_tips: 'experiment_001',
    mlflow_registerModel: 'Register Model',
    mlflow_modelName: 'Model Name',
    mlflow_modelName_tips: 'model_001',
    mlflow_mlflowTrackingUri: 'MLflow Tracking Server URI',
    mlflow_mlflowTrackingUri_tips: 'http://127.0.0.1:5000',
    mlflow_mlflowTrackingUri_error_tips:
      'MLflow Tracking Server URI can not be empty',
    mlflow_jobType: 'Job Type',
    mlflow_automlTool: 'AutoML Tool',
    mlflow_taskType: 'MLflow Task Type',
    mlflow_deployType: 'Deploy Mode',
    mlflow_deployModelKey: 'Model-URI',
    mlflow_deployPort: 'Port',
    mlflowProjectRepository: 'Repository',
    mlflowProjectRepository_tips: 'git repository or path on worker',
    mlflowProjectVersion: 'Project Version',
    mlflowProjectVersion_tips: 'git version',
    mlflow_cpuLimit: 'Max Cpu Limit',
    mlflow_memoryLimit: 'Max Memory Limit',
    openmldb_zk_address: 'zookeeper address',
    openmldb_zk_address_tips: 'Please enter the zookeeper address',
    openmldb_zk_path: 'zookeeper path',
    openmldb_zk_path_tips: 'Please enter the zookeeper path',
    openmldb_execute_mode: 'Execute Mode',
    openmldb_execute_mode_tips: 'Please select the execute mode',
    openmldb_execute_mode_offline: 'offline',
    openmldb_execute_mode_online: 'online',
    dvc_task_type: 'DVC Task Type',
    dvc_repository: 'DVC Repository',
    dvc_repository_tips: 'please input the url of dvc repository',
    dvc_version: 'Version',
    dvc_version_tips: 'data version, will be mark as git tag',
    dvc_data_location: 'Data Path in DVC Repository',
    dvc_message: 'Version Message',
    dvc_load_save_data_path: 'Data Path In Worker',
    dvc_store_url: 'Store Url',
    dvc_empty_tips: 'This parameter cannot be empty',
    dinky_address: 'Dinky address',
    dinky_address_tips: 'Please enter the url of your dinky',
    dinky_task_id: 'Dinky task id',
    dinky_task_id_tips: 'Please enter the task id of your dinky',
    dinky_online: 'Online task',
    pytorch_script: 'Python Script',
    pytorch_script_params: 'Script Input Parameters',
    pytorch_other_params: 'Show More Configurations',
    pytorch_python_path: 'Project Path',
    pytorch_is_create_environment: 'Create An Environment Or Not',
    pytorch_python_command: 'Python Command Path',
    pytorch_python_command_tips: 'If empty，will be set $PYTHON_HOME',
    pytorch_python_env_tool: 'Python Environment Manager Tool',
    pytorch_requirements: 'Requirement File',
    pytorch_conda_python_version: 'Python Version',
    pytorch_conda_python_version_tips:
      'Please enter the version number, such as 3.6, 3.7, 3.x',
    export_dir: 'Export Dir',
    export_dir_tips: 'Please enter Export Dir(required)',
    sql_statement_tips: 'SQL Statement(required)',
    map_column_hive: 'Map Column Hive',
    map_column_java: 'Map Column Java',
    data_target: 'Data Target',
    create_hive_table: 'CreateHiveTable',
    drop_delimiter: 'DropDelimiter',
    over_write_src: 'OverWriteSrc',
    hive_target_dir: 'Hive Target Dir',
    hive_target_dir_tips: 'Please enter hive target dir',
    replace_delimiter: 'ReplaceDelimiter',
    replace_delimiter_tips: 'Please enter Replace Delimiter',
    target_dir: 'Target Dir',
    target_dir_tips: 'Please enter Target Dir(required)',
    delete_target_dir: 'DeleteTargetDir',
    compression_codec: 'CompressionCodec',
    file_type: 'FileType',
    fields_terminated: 'FieldsTerminated',
    fields_terminated_tips: 'Please enter Fields Terminated',
    lines_terminated: 'LinesTerminated',
    lines_terminated_tips: 'Please enter Lines Terminated',
    is_update: 'IsUpdate',
    update_key: 'UpdateKey',
    update_key_tips: 'Please enter Update Key',
    update_mode: 'UpdateMode',
    only_update: 'OnlyUpdate',
    allow_insert: 'AllowInsert',
    concurrency: 'Concurrency',
    concurrency_tips: 'Please enter Concurrency',
    sea_tunnel_master: 'Master',
    sea_tunnel_master_url: 'Master URL',
    sea_tunnel_queue: 'Queue',
    sea_tunnel_master_url_tips:
      'Please enter the master url, e.g., 127.0.0.1:7077',
    switch_condition: 'Condition',
    switch_branch_flow: 'Branch Flow',
    switch_branch_flow_tips: 'Please select branch flow',
    and: 'and',
    or: 'or',
    datax_custom_template: 'Custom Template',
    datax_json_template: 'JSON',
    datax_target_datasource_type: 'Target Datasource Types',
    datax_target_database: 'Target Database',
    datax_target_table: 'Target Table',
    datax_target_table_tips: 'Please enter the name of the target table',
    datax_target_database_pre_sql: 'Pre SQL Statement',
    datax_target_database_post_sql: 'Post SQL Statement',
    datax_non_query_sql_tips: 'Please enter the non-query sql statement',
    datax_job_speed_byte: 'Speed(Byte count)',
    datax_job_speed_byte_info: '(0 means unlimited)',
    datax_job_speed_record: 'Speed(Record count)',
    datax_job_speed_record_info: '(0 means unlimited)',
    datax_job_runtime_memory: 'Runtime Memory Limits',
    datax_job_runtime_memory_xms: 'Low Limit Value',
    datax_job_runtime_memory_xmx: 'High Limit Value',
    datax_job_runtime_memory_unit: 'G',
    chunjun_json_template: 'JSON script',
    current_hour: 'CurrentHour',
    last_1_hour: 'Last1Hour',
    last_2_hour: 'Last2Hours',
    last_3_hour: 'Last3Hours',
    last_24_hour: 'Last24Hours',
    today: 'today',
    last_1_days: 'Last1Days',
    last_2_days: 'Last2Days',
    last_3_days: 'Last3Days',
    last_7_days: 'Last7Days',
    this_week: 'ThisWeek',
    last_week: 'LastWeek',
    last_monday: 'LastMonday',
    last_tuesday: 'LastTuesday',
    last_wednesday: 'LastWednesday',
    last_thursday: 'LastThursday',
    last_friday: 'LastFriday',
    last_saturday: 'LastSaturday',
    last_sunday: 'LastSunday',
    this_month: 'ThisMonth',
    this_month_begin: 'ThisMonthBegin',
    last_month: 'LastMonth',
    last_month_begin: 'LastMonthBegin',
    last_month_end: 'LastMonthEnd',
    month: 'month',
    week: 'week',
    day: 'day',
    hour: 'hour',
    add_dependency: 'Add dependency',
    waiting_dependent_start: 'Waiting Dependent start',
    check_interval: 'Check interval',
    waiting_dependent_complete: 'Waiting Dependent complete',
    project_name: 'Project Name',
    project_name_tips: 'Please select a project(required)',
    process_name: 'Workflow Name',
    process_name_tips: 'Please select a workflow(required)',
    cycle_time: 'Cycle Time',
    cycle_time_tips: 'Please select a cycle time(required)',
    date_tips: 'Please select a date(required)',
    rule_name: 'Rule Name',
    null_check: 'NullCheck',
    custom_sql: 'CustomSql',
    multi_table_accuracy: 'MulTableAccuracy',
    multi_table_value_comparison: 'MulTableCompare',
    field_length_check: 'FieldLengthCheck',
    uniqueness_check: 'UniquenessCheck',
    regexp_check: 'RegexpCheck',
    timeliness_check: 'TimelinessCheck',
    enumeration_check: 'EnumerationCheck',
    table_count_check: 'TableCountCheck',
    src_connector_type: 'SrcConnType',
    src_datasource_id: 'SrcSource',
    src_table: 'SrcTable',
    src_filter: 'SrcFilter',
    src_field: 'SrcField',
    statistics_name: 'ActualValName',
    check_type: 'CheckType',
    operator: 'Operator',
    threshold: 'Threshold',
    failure_strategy: 'FailureStrategy',
    target_connector_type: 'TargetConnType',
    target_datasource_id: 'TargetSourceId',
    target_table: 'TargetTable',
    target_filter: 'TargetFilter',
    mapping_columns: 'OnClause',
    statistics_execute_sql: 'ActualValExecSql',
    comparison_name: 'ExceptedValName',
    comparison_execute_sql: 'ExceptedValExecSql',
    comparison_type: 'ExceptedValType',
    writer_connector_type: 'WriterConnType',
    writer_datasource_id: 'WriterSourceId',
    target_field: 'TargetField',
    field_length: 'FieldLength',
    logic_operator: 'LogicOperator',
    regexp_pattern: 'RegexpPattern',
    deadline: 'Deadline',
    datetime_format: 'DatetimeFormat',
    enum_list: 'EnumList',
    begin_time: 'BeginTime',
    fix_value: 'FixValue',
    fix_value_tips: 'Please set the FixValue',
    required: 'required',
    emr_flow_define_json: 'jobFlowDefineJson',
    emr_flow_define_json_tips: 'Please enter the definition of the job flow.',
    emr_steps_define_json: 'stepsDefineJson',
    emr_steps_define_json_tips: 'Please enter the definition of the emr step.',
    send_email: 'Send Email',
    log_display: 'Log display',
    rows_of_result: 'rows of result',
    title: 'Title',
    title_tips: 'Please enter the title of email',
    alarm_group: 'Alarm group',
    alarm_group_tips: 'Alarm group required',
    integer_tips: 'Please enter a positive integer',
    sql_parameter: 'SQL Parameter',
    format_tips: 'Please enter format',
    udf_function: 'UDF Function',
    unlimited: 'unlimited',
    please_select_source_connector_type: 'Please select source connector type',
    please_select_source_datasource_id: 'Please select source datasource id',
    please_enter_source_table_name: 'Please select source table name',
    please_enter_filter_expression: 'Please enter filter expression',
    please_enter_column_only_single_column_is_supported:
      'Please select column, only single column is supported',
    please_enter_threshold_number_is_needed:
      'Please enter threshold number is needed',
    please_enter_comparison_title: 'please select comparison title',
    please_enter_statistics_execute_sql: 'Please enter statistics execute sql',
    please_enter_statistics_name_the_alias_in_statistics_execute_sql:
      'Please enter statistics name the alias in statistics execute sql',
    please_select_target_connector_type: 'Please select target connector type',
    please_select_target_datasource: 'Please select target datasource',
    please_enter_target_table: 'Please enter target table',
    please_enter_target_filter_expression:
      'Please enter target filter expression',
    please_enter_comparison_name_the_alias_in_comparison_execute_sql:
      'Please enter comparison name the alias in comparison execute sql',
    please_enter_comparison_execute_sql: 'Please enter comparison execute sql',
    please_enter_length_limit: 'Please enter length limit',
    scan_type: 'Scan Type',
    scan_interval: 'Scan Interval',
    file_path: 'File Path',
    file_path_tips: 'File Path(required)',
    topic_name: 'Topic Name',
    topic_name_tips: 'Topic Name(required)',
    offset_time: 'Offset Time',
    offset_time_required_tips:
      // eslint-disable-next-line quotes
      "Offset Time(required) and the format is ${'{'}system.biz.curdate{'}'}, $[yyyyMMdd] or yyyyMMdd",
    offset_time_incorrect_tips:
      // eslint-disable-next-line quotes
      "Please enter the correct offset time format(${'{'}system.biz.curdate{'}'},$[yyyyMMdd],yyyyMMdd).",
    bootstrap_servers: 'Bootstrap Servers',
    bootstrap_servers_tips: 'Bootstrap Servers(required)',
    group_id: 'GroupId',
    group_id_tips: 'GroupId(required)',
    key_deserializer: 'Key Deserializer',
    key_deserializer_tips: 'Key Deserializer(required)',
    value_deserializer: 'Value Deserializer',
    value_deserializer_tips: 'Value Deserializer(required)',
    next_loop_custom_rule: 'Custom Next Loop Rule',
    script_type: 'Script Type',
    next_loop_date_tip:
      'The script return value is in date format: yyyy-MM-dd HH:mm:ss',
    next_loop_timezone_tip:
      'Note that the return date is consistent with the scheduled time zone.',
    next_loop_card: 'Card',
    next_loop_card_tips: 'Card(required)',
    dependency_strategy: 'Dependency Strategy',
    failure: 'Failure',
    continue: 'Continue',
    wait: 'Wait',
    failure_continue_tips:
      'When a dependency fails, the current node continues execution.',
    failure_wait_tips:
      'When a dependency fails, the current node waits for the dependency to succeed and then continues execution.',
    hive_cli_task_execution_type: 'Hive Cli task type',
    hive_sql_script: 'Hive SQL script',
    hive_cli_options: 'Hive Cli options',
    hive_cli_options_tips: 'Please input your HIVE CLI options, like --verbose',
    natural: 'Natural Day',
    business: 'Business Date',
    card: 'Card',
    rename: 'Rename',
    name_node: 'Name',
    custom_config: 'Custom Config',
    engine: 'engine',
    engine_tips: 'Please select engine',
    run_mode: 'Run Mode',
    cart_null_tips: 'The workflow has not set the card value',
    offline_task: 'Offline Task',
    stream_task: 'Stream Task',
    whale_seatunnel_task_tips: 'Please select task',
    execute_type: 'Execute Type',
    java_option_tips: 'Please input Java Option',
    dependent_path: 'Dependent Package Path',
    dependent_path_tips: 'Please input dependent package path',
    local_file: 'Local File',
    git_file: 'Git File',
    break_continue: 'Break continue',
    remote_connection: 'Remote Connection',
    fake_connection: 'Fake Connection',
    connection_type: 'Connection Type',
    source_name: 'Source Name',
    ssh: 'SSH',
    connection_type_tips: 'Connection type is required',
    source_name_tips: 'Source name is required',
    folder_name: 'Folder Name',
    mapping_name: 'Informatica Workflow Name',
    target_name: 'Target Name',
    folder_name_tips: 'Please choose folder name',
    mapping_name_tips: 'Please choose informatica workflow name',
    source_type_tips: 'Please choose source type',
    target_type_tips: 'Please choose target type',
    source_name_tips_infa: 'Please enter source name',
    target_name_tips: 'Please enter target name'
  },
  isolation: {
    upload_isolation_tasks: 'Upload Isolation Tasks',
    download_template: 'Download Template',
    task_name: 'Task Name',
    workflow_instance_name: 'Workflow Instance Name',
    operation_status: 'Operation Status',
    create_time: 'Create Time',
    online_isolation: 'Online Isolation',
    cancel_isolation: 'Cancel Isolation',
    task_isolation: 'Task Isolation',
    local_upload: 'Local Upload',
    isolation_tasks: 'Isolation tasks',
    show_more: 'Show More',
    previous: 'Previous',
    next: 'Next',
    upload_tips: 'Only supports excel, and only supports one file at a time.',
    sure: 'Sure',
    cancel: 'Cancel',
    operation: 'Operation',
    online: 'Online',
    offline: 'Offline',
    total_items: 'Total {total} items',
    file_tips: 'Please select an excel file.'
  },
  synchronization_definition: {
    node_prev_check_tips: 'The current node is not connected to the previous node',
    create_synchronization_task: 'Create Synchronization Task',
    edit_synchronization_task: 'Edit Synchronization Task',
    synchronization_task_name: 'Synchronization Task Name',
    task_describe: 'Task Describe',
    create_user: 'Create User',
    create_time: 'Create Time',
    update_user: 'Update User',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    start: 'start',
    delete: 'Delete',
    delete_confirm: 'Delete?',
    task_name_tips: 'Please entry task name',
    task_describe_tips: 'Please entry task describe',
    task_name_validate: 'Task name cannot be empty',
    copy: 'Copy',
    open_full_screen: 'Open Full Screen',
    close_full_screen: 'Close Full Screen',
    format: 'Format',
    save: 'Save',
    close: 'Close',
    layout_type: 'Layout Type',
    rows: 'Rows',
    cols: 'Cols',
    dagre: 'Dagre',
    grid: 'Grid',
    format_canvas: 'Format Canvas',
    setting: 'Setting',
    task_name: 'Task Name',
    description: 'Description',
    engine: 'Engine',
    task_name_placeholder: 'Please entry task name',
    description_placeholder: 'Please entry description',
    node_name_validate: 'Node name is not empty',
    node_name: 'Node Name',
    node_name_placeholder: 'Please entry node name',
    transforms: 'Transforms',
    source_and_sink: 'Source / Sink',
    confirm: 'Confirm',
    cancel: 'Cancel',
    configuration: 'Configuration',
    model: 'Model',
    business_model: 'Business Model',
    whole_library_sync: 'Whole Library Synchronization',
    data_integration: 'Data Integration',
    source_name: 'Source Name',
    source_name_validate: 'Source name is not empty',
    scene_mode: 'Scene Mode',
    multi_table_sync: 'Multi Table Synchronization',
    sub_library_and_sub_table: 'Sub-library And Sub-table',
    single_table_sync: 'Single Table Synchronization',
    database: 'Database',
    table_name: 'Table Name',
    field_name: 'Field Name',
    field_type: 'Field Type',
    non_empty: 'Non Empty',
    field_comment: 'Field Comment',
    input_table_structure: 'Input Table Structure',
    output_table_structure: 'Output Table Structure',
    exclude_kind: 'Exclude Kind',
    include_kind: 'Include Kind',
    exclude_kind_validate: 'Exclude kind is not empty',
    include_kind_validate: 'Include kind is not empty',
    database_validate: 'Database is not empty',
    table_name_validate: 'Table name is not empty',
    primary_key: 'Primary Key',
    default_value: 'Default Value',
    scene_mode_validate: 'Scene mode is not empty',
    kind: 'Kind',
    delete_empty_tips: 'Please select a node',
    database_exception_message:
      'database exception，has been cleared in the form',
    table_exception_message: 'table exception，has been cleared in the form',
    start_node_tips: 'The starting node be source node',
    end_node_tips: 'The ending node be sink node',
    save_node_tips: 'Please save the nodes on the canvas first',
    table_sync: 'Table to be synchronized',
    selected_table: 'Selected table',
    original_field: 'Original Field',
    name_tips: 'Please enter a field name',
    move_to_top: 'Move to top',
    move_to_bottom: 'Move to bottom',
    move_up: 'Move up',
    move_down: 'Move down',
    yes: 'Yes',
    no: 'No',
    split: 'MultiFieldSplit',
    split_field: 'Split Field',
    separator: 'Separator',
    separator_tips: 'Please enter a separator',
    segmented_fields: 'Segmented fields',
    segmented_fields_placeholder: 'If two fields are separated, you can fill in field1, field2',
    copy_field: 'Copy Field',
    check_model: 'Please check the model information',
    sql_content_label: 'sql',
    sql_content_label_placeholder: 'please input the SQL statement',
    query_validate: 'please input the SQL statement',

  },
  synchronization_instance: {
    pipeline_id: 'Pipeline Id',
    source: 'Source',
    sink: 'Sink',
    run_often: 'Run Often',
    fail: 'Fail',
    running: 'Running',
    pause: 'Pause',
    success: 'Success',
    state: 'State',
    start_time: 'Start Time',
    end_time: 'End Time',
    operation: 'Operation',
    real_time_sync: 'Real Time Sync',
    offline_sync: 'Offline Sync',
    sync_task_definition: 'Sync Task Definition',
    data_pipeline_running_instance: 'Data Pipeline Running Instance',
    task_name: 'Task Name',
    workflow_instance: 'Workflow Instance',
    execute_user: 'Execute User',
    host: 'Host',
    amount_of_data_read: 'Amount Of Data Read (Row)',
    read_rate: 'Read Rate (Row / Second)',
    processing_rate: 'Processing Rate (Row / Second)',
    amount_of_data_written: 'Amount Of Data Written (Row)',
    delay_of_data: 'Delay Of Data (Second)',
    node_type: 'Node Type',
    submit_time: 'Submit Time',
    run_time: 'Run Time',
    number_of_retries: 'Number Of Retries',
    rerun_mark: 'Rerun Mark',
    clean_state: 'Clear and run again',
    forced_success: 'Forced Success',
    view_log: 'View Log',
    download_log: 'Download Log',
    description: 'Description',
    engine: 'Engine',
    write: 'Write',
    read: 'Read',
    line: 'lines',
    confirm: 'Confirm',
    cancel: 'Cancel',
    delete: 'Delete',
    delete_confirm: 'Delete?',
    error_message: 'Error'
  },
  menu: {
    fav: 'Favorites',
    universal: 'Universal',
    cloud: 'Cloud',
    logic: 'Logic',
    di: 'Data Integration',
    dq: 'Data Quality',
    ml: 'Machine Learning',
    other: 'Other'
  },
  member: {
    alias: 'Alias',
    role_name: 'Role Name',
    member_manage: 'Member Manage',
    new_member: 'New Member',
    remove_member: 'Remove Member',
    confirm: 'Confirm',
    cancel: 'Cancel',
    edit: 'Edit',
    operation: 'Operation'
  },
  synchronizing_task_instance: 'Synchronizing task instances',
  task_instance: 'Task instance',
  column: 'Column',
  all_column: 'All',
  select_workflow_instance: 'Select the workflow instance',
  select_workflow_definition: 'Select the workflow definition',
  select_task_instance: 'Select the task instance',
  can_not_many: 'Cross-project operations are not yet supported',
  all_project: 'All projects',
  next_step: 'Next step',
  pre_step: 'Last step',
  project_name: 'Project Name'
}
