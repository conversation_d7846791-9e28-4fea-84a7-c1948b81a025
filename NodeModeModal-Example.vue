<template>
  <div class="node-mode-modal-example">
    <el-card class="demo-card">
      <div slot="header" class="card-header">
        <span>NodeModeModal 组件演示</span>
        <div class="demo-controls">
          <el-select v-model="demoConfig.type" placeholder="选择节点类型" size="small">
            <el-option label="Source" value="source" />
            <el-option label="Transform" value="transform" />
            <el-option label="Sink" value="sink" />
          </el-select>
          <el-select v-model="demoConfig.transformType" placeholder="选择转换类型" size="small">
            <el-option label="无" value="" />
            <el-option label="FieldMapper" value="FieldMapper" />
            <el-option label="MultiFieldSplit" value="MultiFieldSplit" />
            <el-option label="Copy" value="Copy" />
            <el-option label="Sql" value="Sql" />
          </el-select>
          <el-button type="primary" size="small" @click="initDemo">
            初始化演示数据
          </el-button>
          <el-button type="success" size="small" @click="getResults">
            获取结果
          </el-button>
        </div>
      </div>

      <!-- NodeModeModal 组件 -->
      <NodeModeModal
        ref="nodeModeModal"
        :type="demoConfig.type"
        :transform-type="demoConfig.transformType"
        :predecessors-node-id="demoConfig.predecessorsNodeId"
        :current-node-id="demoConfig.currentNodeId"
        :schema-error="demoConfig.schemaError"
        :ref-form="mockFormRef"
      />
    </el-card>

    <!-- 结果显示 -->
    <el-card class="result-card" v-if="results">
      <div slot="header">
        <span>组件结果</span>
      </div>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="选中字段" name="selected">
          <pre>{{ JSON.stringify(results.selectedFields, null, 2) }}</pre>
        </el-tab-pane>
        <el-tab-pane label="输出结构" name="output">
          <pre>{{ JSON.stringify(results.outputSchema, null, 2) }}</pre>
        </el-tab-pane>
        <el-tab-pane label="组件状态" name="state">
          <pre>{{ JSON.stringify(results.componentState, null, 2) }}</pre>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 配置面板 -->
    <el-drawer
      title="配置面板"
      :visible.sync="configDrawerVisible"
      direction="rtl"
      size="400px"
    >
      <div class="config-panel">
        <el-form :model="demoConfig" label-width="120px" size="small">
          <el-form-item label="节点类型">
            <el-select v-model="demoConfig.type" placeholder="选择节点类型">
              <el-option label="Source" value="source" />
              <el-option label="Transform" value="transform" />
              <el-option label="Sink" value="sink" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="转换类型">
            <el-select v-model="demoConfig.transformType" placeholder="选择转换类型">
              <el-option label="无" value="" />
              <el-option label="FieldMapper" value="FieldMapper" />
              <el-option label="MultiFieldSplit" value="MultiFieldSplit" />
              <el-option label="Copy" value="Copy" />
              <el-option label="Sql" value="Sql" />
            </el-select>
          </el-form-item>

          <el-form-item label="前置节点ID">
            <el-input v-model="demoConfig.predecessorsNodeId" placeholder="输入前置节点ID" />
          </el-form-item>

          <el-form-item label="当前节点ID">
            <el-input v-model="demoConfig.currentNodeId" placeholder="输入当前节点ID" />
          </el-form-item>

          <el-form-item label="可选择列">
            <el-switch v-model="demoConfig.columnSelectable" />
          </el-form-item>

          <el-form-item label="数据库">
            <el-input v-model="demoConfig.database" placeholder="输入数据库名" />
          </el-form-item>

          <el-form-item label="数据源ID">
            <el-input v-model="demoConfig.datasourceInstanceId" placeholder="输入数据源ID" />
          </el-form-item>

          <el-form-item label="表列表">
            <el-input
              v-model="demoConfig.tablesStr"
              type="textarea"
              :rows="3"
              placeholder="输入表名，用逗号分隔"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="applyConfig">应用配置</el-button>
            <el-button @click="resetConfig">重置配置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>

    <!-- 浮动按钮 -->
    <el-button
      class="config-button"
      type="primary"
      icon="el-icon-setting"
      circle
      @click="configDrawerVisible = true"
    />
  </div>
</template>

<script>
import NodeModeModal from './NodeModeModal.vue'

export default {
  name: 'NodeModeModalExample',
  components: {
    NodeModeModal
  },
  data() {
    return {
      // 演示配置
      demoConfig: {
        type: 'source',
        transformType: '',
        predecessorsNodeId: 'source_001',
        currentNodeId: 'transform_001',
        schemaError: {},
        columnSelectable: true,
        database: 'demo_db',
        datasourceInstanceId: 'mysql_demo',
        tablesStr: 'users,orders,products'
      },
      
      // 模拟表单引用
      mockFormRef: {
        getValues: () => ({
          query: 'SELECT id, name, email FROM users WHERE status = 1'
        })
      },

      // 结果数据
      results: null,
      activeTab: 'selected',
      
      // UI 状态
      configDrawerVisible: false,

      // 模拟数据
      mockTableData: {
        users: [
          { name: 'id', type: 'bigint', comment: '用户ID', nullable: false, primaryKey: true, defaultValue: '' },
          { name: 'username', type: 'varchar(50)', comment: '用户名', nullable: false, primaryKey: false, defaultValue: '' },
          { name: 'email', type: 'varchar(100)', comment: '邮箱', nullable: true, primaryKey: false, defaultValue: '' },
          { name: 'phone', type: 'varchar(20)', comment: '手机号', nullable: true, primaryKey: false, defaultValue: '' },
          { name: 'status', type: 'tinyint', comment: '状态', nullable: false, primaryKey: false, defaultValue: '1' },
          { name: 'created_at', type: 'datetime', comment: '创建时间', nullable: false, primaryKey: false, defaultValue: 'CURRENT_TIMESTAMP' },
          { name: 'updated_at', type: 'datetime', comment: '更新时间', nullable: false, primaryKey: false, defaultValue: 'CURRENT_TIMESTAMP' }
        ],
        orders: [
          { name: 'id', type: 'bigint', comment: '订单ID', nullable: false, primaryKey: true, defaultValue: '' },
          { name: 'user_id', type: 'bigint', comment: '用户ID', nullable: false, primaryKey: false, defaultValue: '' },
          { name: 'order_no', type: 'varchar(32)', comment: '订单号', nullable: false, primaryKey: false, defaultValue: '' },
          { name: 'total_amount', type: 'decimal(10,2)', comment: '总金额', nullable: false, primaryKey: false, defaultValue: '0.00' },
          { name: 'status', type: 'varchar(20)', comment: '订单状态', nullable: false, primaryKey: false, defaultValue: 'pending' },
          { name: 'created_at', type: 'datetime', comment: '创建时间', nullable: false, primaryKey: false, defaultValue: 'CURRENT_TIMESTAMP' }
        ],
        products: [
          { name: 'id', type: 'bigint', comment: '产品ID', nullable: false, primaryKey: true, defaultValue: '' },
          { name: 'name', type: 'varchar(100)', comment: '产品名称', nullable: false, primaryKey: false, defaultValue: '' },
          { name: 'price', type: 'decimal(8,2)', comment: '价格', nullable: false, primaryKey: false, defaultValue: '0.00' },
          { name: 'category', type: 'varchar(50)', comment: '分类', nullable: true, primaryKey: false, defaultValue: '' },
          { name: 'description', type: 'text', comment: '描述', nullable: true, primaryKey: false, defaultValue: '' },
          { name: 'stock', type: 'int', comment: '库存', nullable: false, primaryKey: false, defaultValue: '0' }
        ]
      }
    }
  },
  mounted() {
    // 模拟 API 方法
    this.mockApiMethods()
    // 初始化演示数据
    this.initDemo()
  },
  methods: {
    // 模拟 API 方法
    mockApiMethods() {
      this.$api = {
        queryTaskDetail: (jobCode, taskCode) => {
          return Promise.resolve({
            outputSchema: [
              {
                tableName: 'users',
                database: 'demo_db',
                fields: this.mockTableData.users
              }
            ],
            transformOptions: {}
          })
        },
        
        modelInfo: (datasourceId, data) => {
          const tables = data[0].tables || []
          const tableInfos = tables.map(tableName => ({
            tableName,
            database: data[0].database,
            fields: this.mockTableData[tableName] || []
          }))
          
          return Promise.resolve([{
            database: data[0].database,
            tableInfos
          }])
        },
        
        sqlModelInfo: (taskId, pluginId, data) => {
          return Promise.resolve({
            fields: [
              { name: 'id', type: 'bigint', comment: '用户ID' },
              { name: 'name', type: 'varchar(50)', comment: '姓名' },
              { name: 'email', type: 'varchar(100)', comment: '邮箱' }
            ]
          })
        }
      }
    },

    // 初始化演示数据
    initDemo() {
      const tables = this.demoConfig.tablesStr.split(',').map(t => t.trim()).filter(t => t)
      
      const initData = {
        tables,
        columnSelectable: this.demoConfig.columnSelectable,
        database: this.demoConfig.database,
        datasourceInstanceId: this.demoConfig.datasourceInstanceId,
        format: 'DEFAULT',
        transformOptions: {},
        outputSchema: []
      }

      this.$refs.nodeModeModal.initData(initData)
      this.$message.success('演示数据初始化完成')
    },

    // 获取组件结果
    getResults() {
      const selectedFields = this.$refs.nodeModeModal.getSelectFields()
      const outputSchema = this.$refs.nodeModeModal.getOutputSchema()
      const componentState = this.$refs.nodeModeModal.state

      this.results = {
        selectedFields,
        outputSchema,
        componentState
      }

      this.$message.success('结果获取成功')
    },

    // 应用配置
    applyConfig() {
      this.initDemo()
      this.configDrawerVisible = false
    },

    // 重置配置
    resetConfig() {
      this.demoConfig = {
        type: 'source',
        transformType: '',
        predecessorsNodeId: 'source_001',
        currentNodeId: 'transform_001',
        schemaError: {},
        columnSelectable: true,
        database: 'demo_db',
        datasourceInstanceId: 'mysql_demo',
        tablesStr: 'users,orders,products'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.node-mode-modal-example {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f7fa;

  .demo-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .demo-controls {
        display: flex;
        gap: 12px;
        align-items: center;
        
        .el-select {
          width: 140px;
        }
      }
    }
  }

  .result-card {
    pre {
      background-color: #f8f9fa;
      padding: 16px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 1.5;
      max-height: 400px;
      overflow-y: auto;
    }
  }

  .config-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
  }

  .config-panel {
    padding: 20px;
    
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}

::v-deep .el-drawer__body {
  padding: 0;
}
</style>
