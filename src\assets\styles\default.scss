/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

 $box-node-content: "Double click to configure. After configure, Connect each node ends to another.";

.x6-graph-scroller {
  overflow: hidden !important;
  &:hover {
    overflow: scroll !important;
  }
}
.x6-graph-scroller::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.x6-graph-scroller::-webkit-scrollbar-thumb {
  border-radius: 5px;
  transition: all 0.2s;
  background-color: rgba(0, 0, 0, 0.25);
}
.x6-widget-selection-box.x6-widget-selection-box-node:hover {
  position: relative;
  &::before {
    content: $box-node-content;
    width: 210px;
    height: 40px;
    background: #eeecec;
    color: #131313;
    border-radius: 5px;
    position: absolute;
    top: -45px;
    right: -25px;
    text-align: center;
    box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.5);
    font-size: 11.5px;
    line-height: 1.1;
    padding: 3px;
  }
}
.task-item-info {
  cursor: help;
}
body{
  height: 100%;
}
#app{
  height: 100%;
}