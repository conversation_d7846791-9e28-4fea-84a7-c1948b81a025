/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  virtual_tables: '虚拟表',
  create_virtual_tables: '创建虚拟表',
  edit_virtual_tables: '编辑虚拟表',
  source_type: '源类型',
  source_type_tips: '请选择源类型',
  source_name: '源名称',
  source_name_tips: '请输入源名称',
  table_name: '表名',
  database_name: '库名',
  creator: '创建人',
  creation_time: '创建时间',
  updater: '修改人',
  update_time: '修改时间',
  operation: '操作',
  edit: '编辑',
  delete: '删除',
  confirm: '确定',
  delete_confirm: '删除？',
  cancel: '取消',
  configure: '配置',
  model: '模型',
  complete: '完成',
  virtual_tables_name: '虚拟表名',
  virtual_tables_name_tips: '请输入虚拟表名',
  next_step: '下一步',
  previous_step: '上一步',
  table_structure: '表结构',
  add: '添加一行',
  field_name: '字段名称',
  field_name_tips: '请输入字段名称',
  field_type: '字段类型',
  is_null: '非空',
  is_primary_key: '主键',
  description: '字段描述',
  yes: '是',
  no: '否',
  warning: '警告',
  close_confirm_tips: '此操作会丢失当前创建的虚拟表',
  save_data_tips: '请保存表格中的数据',
  table_data_required_tips: '请在表结构中添加数据',
  default_value: '默认值',
  create: '创建',
  search: '搜索'
}
