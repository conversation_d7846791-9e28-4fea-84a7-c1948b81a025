/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  tenant: {
    tenant_manage: '租户管理',
    create_tenant: '创建租户',
    search_tips: '请输入关键词',
    tenant_code: '操作系统租户',
    tenant_id: '租户ID',
    description: '描述',
    queue_name: '队列',
    create_time: '创建时间',
    update_time: '更新时间',
    actions: '操作',
    edit_tenant: '编辑租户',
    tenant_code_tips: '请输入操作系统租户',
    queue_name_tips: '请选择队列',
    description_tips: '请输入描述',
    delete_confirm: '确定删除吗?',
    edit: '编辑',
    delete: '删除'
  },
  alarm_group: {
    create_alarm_group: '创建告警组',
    edit_alarm_group: '编辑告警组',
    search_tips: '请输入关键词',
    alert_group_name_tips: '请输入告警组名称',
    alarm_plugin_instance: '告警组实例',
    alarm_plugin_instance_tips: '请选择告警组实例',
    alarm_group_description_tips: '请输入告警组描述',
    alert_group_name: '告警组名称',
    alarm_group_description: '告警组描述',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    delete_confirm: '确定删除吗?',
    edit: '编辑',
    delete: '删除'
  },
  worker_group: {
    create_worker_group: '创建Worker分组',
    edit_worker_group: '编辑Worker分组',
    search_tips: '请输入关键词',
    operation: '操作',
    delete_confirm: '确定删除吗?',
    edit: '编辑',
    delete: '删除',
    group_name: '分组名称',
    group_name_tips: '请输入分组名称',
    worker_addresses: 'Worker地址',
    worker_addresses_tips: '请选择Worker地址',
    create_time: '创建时间',
    update_time: '更新时间',
    description: '描述',
    description_tips: '请输入描述'
  },
  yarn_queue: {
    create_queue: '创建队列',
    edit_queue: '编辑队列',
    search_tips: '请输入关键词',
    queue_name: '队列名',
    queue_value: '队列值',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    edit: '编辑',
    queue_name_tips: '请输入队列名（不区分大小写）',
    queue_value_tips: '请输入队列值'
  },
  environment: {
    create_environment: '创建环境',
    edit_environment: '编辑环境',
    search_tips: '请输入关键词',
    edit: '编辑',
    delete: '删除',
    environment_name: '环境名称',
    environment_config: '环境配置',
    environment_desc: '环境描述',
    worker_groups: 'Worker分组',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    delete_confirm: '确定删除吗?',
    environment_name_tips: '请输入环境名',
    environment_config_tips: '请输入环境配置',
    environment_description_tips: '请输入环境描述',
    worker_group_tips: '请选择Worker分组'
  },
  token: {
    create_token: '创建令牌',
    edit_token: '编辑令牌',
    search_tips: '请输入关键词',
    user: '用户',
    user_tips: '请选择用户',
    token: '令牌',
    token_tips: '请点击获取令牌',
    expiration_time: '失效时间',
    expiration_time_tips: '请选择失效时间',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    edit: '编辑',
    delete: '删除',
    delete_confirm: '确定删除吗?'
  },
  user: {
    user_manage: '用户管理',
    create_user: '创建用户',
    edit_user: '编辑用户',
    delete_user: '删除用户',
    delete_confirm: '确定删除吗?',
    project: '项目',
    resource: '资源',
    file_resource: '文件资源',
    udf_resource: 'UDF资源',
    datasource: '数据源',
    udf: 'UDF函数',
    authorize_project: '项目授权',
    authorize_resource: '资源授权',
    authorize_datasource: '数据源授权',
    authorize_udf: 'UDF函数授权',
    // username: '用户名',
    username_exists: '用户名已存在',
    username_tips: '请输入用户名',
    tenant_tips: '请选择租户',
    user_password: '密码',
    user_password_tips: '请输入包含字母和数字，长度在6～20之间的密码',
    user_type: '用户类型',
    ordinary_user: '普通用户',
    administrator: '管理员',
    tenant_code: '租户',
    queue: '队列',
    queue_tips: '默认为租户关联队列',
    email: '邮件',
    email_empty_tips: '请输入邮箱',
    emial_correct_tips: '请输入正确的邮箱格式',
    phone: '手机',
    phone_empty_tips: '请输入手机号码',
    phone_correct_tips: '请输入正确的手机格式',
    state: '状态',
    state_enabled: '启用',
    state_disabled: '停用',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    edit: '编辑',
    delete: '删除',
    authorize: '授权',
    save_error_msg: '保存失败，请重试',
    delete_error_msg: '删除失败，请重试',
    auth_error_msg: '授权失败，请重试',
    auth_success_msg: '授权成功',
    enable: '启用',
    disable: '停用',
    association_permission: '关联权限',
    view_permission: '查看权限',
    reset_password: '重置密码',
    import_user: '导入用户',
    download_template: '下载模板',
    alias: '用户名称',
    username: '用户账号',
    roles_owned: '所属角色',
    description: '描述',
    association_role: '关联角色',
    account_password: '账号密码',
    reset_confirm: '确定重置密码吗?',
    all_role: '所有角色',
    success: '成功',
    import_success: '用户导入成功',
    reset_req_success: '密码重置请求成功，请检查您的邮件',
    reset_success: '密码重置成功',
    search_tips: '请输入关键词',
    alias_tips: '请输入用户名称',
    association_role_tips: '请选择关联角色',
    tenant_id_tips: '请选择租户',
    description_tips: '请输入描述',
    check_error: '用户导入校验未通过',
    user_account: '用户账号',
    user_name: '用户名称',
    system_admin: '系统管理员',
    project_role: '项目角色',
    none: '无'
  },
  role: {
    role_name: '角色名称',
    associated_users: '关联用户数',
    create_time: '创建时间',
    operation: '操作',
    check_authority: '查看权限',
    rename: '重命名',
    distribute_authority: '分配权限',
    delete: '删除',
    delete_confirm: '确定删除吗？',
    create_role: '创建角色',
    name_tips: '请输入角色名称',
    search_tips: '搜索角色',
    functional_authority: '功能权限',
    resource_authority: '资源权限',
    name: '名称',
    type: '类型',
    current_role: '当前角色',
    confirm: '确定',
    cancel: '取消',
    function_resource_empty_tips: '请选择功能权限或者资源权限！',
    success: '成功',
    module: '模块',
    page: '页面',
    function: '功能'
  },
  alarm_instance: {
    search_input_tips: '请输入关键字',
    alarm_instance_manage: '告警实例管理',
    alarm_instance_name: '告警实例名称',
    alarm_instance_name_tips: '请输入告警实例名称',
    alarm_plugin_name: '告警插件名称',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    edit_alarm_instance: '编辑告警实例',
    delete: '删除',
    edit: '编辑',
    delete_confirm: '删除？',
    confirm: '确定',
    cancel: '取消',
    submit: '提交',
    create_alarm_instance: '创建告警实例',
    select_plugin: '选择插件',
    select_plugin_tips: '请选择告警插件',
    instance_parameter_exception: '实例参数异常',
    WebHook: 'Web钩子',
    webHook: 'Web钩子',
    WarningType: '告警类型',
    IsEnableProxy: '启用代理',
    Proxy: '代理',
    Port: '端口',
    User: '用户',
    corpId: '企业ID',
    secret: '密钥',
    Secret: '密钥',
    users: '群员',
    userSendMsg: '群员信息',
    'agentId/chatId': '应用ID或群聊ID',
    showType: '内容展示类型',
    receivers: '收件人',
    receiverCcs: '抄送人',
    serverHost: 'SMTP服务器',
    serverPort: 'SMTP端口',
    sender: '发件人',
    enableSmtpAuth: '请求认证',
    Password: '密码',
    starttlsEnable: 'STARTTLS连接',
    sslEnable: 'SSL连接',
    smtpSslTrust: 'SSL证书信任',
    url: 'URL',
    requestType: '请求方式',
    headerParams: '请求头',
    bodyParams: '请求体',
    contentField: '内容字段',
    Keyword: '关键词',
    userParams: '自定义参数',
    path: '脚本路径',
    type: '类型',
    sendType: '发送类型',
    username: '用户名',
    botToken: '机器人Token',
    chatId: '频道ID',
    parseMode: '解析类型',
    IntegrationKey: '集成密钥',
    BotAccessToken: '访问令牌',
    RoomId: '房间',
    ToPersonId: '用户',
    ToPersonEmail: '用户邮箱',
    // eslint-disable-next-line quotes
    AtSomeoneInRoom: "{'@'}房间中的成员",
    Destination: '描述',
    // eslint-disable-next-line quotes
    AtMobiles: "被{'@'}人的手机号",
    // eslint-disable-next-line quotes
    AtUserIds: "被{'@'}人的用户ID",
    MsgType: '消息类型',
    // eslint-disable-next-line quotes
    IsAtAll: "{'@'}所有人"
  },
  k8s_namespace: {
    create_namespace: '创建命名空间',
    edit_namespace: '编辑命名空间',
    search_tips: '请输入关键词',
    k8s_namespace: 'K8S命名空间',
    k8s_namespace_tips: '请输入k8s命名空间',
    k8s_cluster: 'K8S集群',
    k8s_cluster_tips: '请输入k8s集群',
    owner: '负责人',
    owner_tips: '请输入负责人',
    tag: '标签',
    tag_tips: '请输入标签',
    limit_cpu: '最大CPU',
    limit_cpu_tips: '请输入最大CPU',
    limit_memory: '最大内存',
    limit_memory_tips: '请输入最大内存',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    edit: '编辑',
    delete: '删除',
    delete_confirm: '确定删除吗?'
  },
  calendar: {
    calendar: '日历',
    create: '创建日历',
    edit_calendar: '编辑日历',
    edit: '编辑',
    delete: '删除',
    delete_confirm: '确定删除吗?',
    search_tips: '请输入关键词',
    name: '日历名称',
    operation: '操作',
    cancel: '取消',
    confirm: '确定',
    type: '日历类型',
    description: '日历描述',
    name_tips: '请输入日历名称',
    description_tips: '请输入日历描述',
    import_calendar: '导入日历',
    export_calendar_template: '导出日历模板',
    select_date: '选择日期',
    select_date_tips: '请选择日期',
    no_execution: '不执行',
    execution: '执行',
    calender_config_rules: '日历配置规则',
    upload_tips: '点击或者拖动文件到该区域来上传',
    import_success_tips: '日期导入成功',
    import_failure_tips: '日期导入失败',
    file_wrong_format: '上传的文件格式错误',
    date_wrong_format_part: '部分日期格式不正确',
    date_wrong_format: '日期格式不正确',
    import_empty_file: '导入了一个空文件',
    import_partial_failure_tips: '部分日期导入失败'
  },
  card: {
    create_card: '创建牌',
    edit_card: '修改牌',
    card_name: '牌名称',
    calendar: '日历',
    card_value: '牌值',
    flop_direction: '翻牌方向',
    day_step: '一次翻牌跳过的天',
    hour_step: '一次翻牌跳过的小时',
    minute_step: '一次翻牌跳过的分钟',
    output_format: '牌输出格式',
    card_process_definition_name: '所属工作流',
    card_user_name: '所属用户',
    create_time: '创建时间',
    update_time: '修改时间',
    operation: '操作',
    edit: '编辑',
    edit_value: '编辑牌值',
    delete: '删除',
    forward: '向前',
    backward: '向后',
    day: '天',
    hour: '小时',
    minute: '分钟',
    delete_confirm: '确定删除吗?',
    card_name_tips: '请输入牌名称',
    calendar_tips: '请选择日历',
    card_value_tips: '请输入牌值',
    output_format_tips: '请选择牌输出格式',
    day_step_tips: '请输入一次翻牌跳过日历的天',
    hour_step_tips: '请输入一次翻牌跳过日历的小时',
    minute_step_tips: '请输入一次翻牌跳过日历的分钟',
    step_feedback: '翻牌跳过的时间不能全部是0',
    notification_strategy: '通知策略',
    alarm_group: '告警组',
    please_choose: '请选择',
    description: '描述',
    description_tips: '请输入牌描述'
  },
  label: {
    label: '标签',
    create_label: '创建标签',
    edit_label: '编辑标签',
    edit: '编辑',
    delete: '删除',
    delete_confirm: '确定删除吗?',
    search_tips: '请输入关键词',
    label_name: '标签名称',
    label_color: '标签颜色',
    operation: '操作',
    cancel: '取消',
    confirm: '确定',
    label_description: '标签描述',
    label_name_tips: '请输入标签名称',
    label_description_tips: '请输入标签描述',
    label_code: '标签Code',
    label_code_tips: '请输入标签Code',
    label_color_tips: '请选择标签颜色'
  },
  audit: {
    export: '导出',
    export_excel: '导出Excel',
    alias: '用户名称',
    username: '用户账号',
    operate: '操作',
    operate_time: '操作时间',
    export_date: '请选择导出日期',
    export_date_tips: '请选择导出日期',
    export_tips: '默认导出格式为xlsx，导出日期范围是允许导出近三个月的数据。'
  },
  timing: {
    create_timing: '创建定时',
    edit_timing: '编辑定时',
    search_tips: '请输入关键词',
    timing_name: '定时名称',
    by_using_the_number_tip1: '已知有',
    by_using_the_number_tip2:
      '个工作流关联到该定时，编辑成功后，被关联的工作流会按新的定时执行！',
    crontab: 'Crontab',
    start_time: '开始时间',
    end_time: '结束时间',
    by_using_the_number: '当前被使用次数',
    creator: '创建人',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    edit: '编辑',
    delete: '删除',
    delete_confirm: '确定删除吗?',
    timing_name_tips: '请输入定时名称',
    time_tips: '请选择起止时间',
    start_and_stop_time: '起止时间',
    timezone: '时区',
    calender: '日历',
    timing: '定时',
    calender_tips: '请选择日历',
    next_five_execution_times: '接下来五次执行时间',
    next_five_execution_times_tips: '根据当前配置，没有可执行的时间',
    project_name: '项目名称',
    workflow_name: '工作流名称',
    on_off_status: '上/下线状态',
    online: '上线',
    offline: '下线',
    times: '次',
    timing_used: '有以下工作流使用了该定时',
    cut_day_time: '日切时间'
  },
  packaged_deployment: {
    import: '导入',
    export: '导出',
    packaged_deployment: '打包部署',
    name: '名称',
    update_time: '更新时间',
    creator: '创建人',
    error_messages: '错误信息',
    serial_number: '序号',
    upload_tips: '点击或者拖动文件到该区域来上传',
    file_tips: '文件必填',
    close: '关闭',
    workflow: '工作流',
    export_empty_tips: '需要选择工作流',
    import_success: '导入成功',
    not_support_type: '文件类型不支持'
  },
  license: {
    license_manage: 'License管理',
    license_code: 'License授权码',
    refresh_system_id: '刷新系统标识',
    system_id: '系统标识码',
    enter_license_code: '输入License授权码',
    license_days: 'License有效天数',
    license_time: 'License时效范围',
    contact: '联系客服申请有效授权码',
    refresh: '更新',
    to: '至',
    day: '天',
    remaining: '剩余',
    tips_success: 'License授权码更新成功',
    tips_jump: '后跳转到登录页',
    seconds: '秒',
    close: '关闭',
    system_tips: '系统提示',
    warning_tips: '您的License有效天数还有',
    warning_tips2: '请及时联系管理员更新License授权码,以免影响系统使用',
    warning_tips3: '您的License不存在/已过期,请尽快联系管理员更新License授权码',
    warning_tips4: '授权码更新失败',
    version: '版本',
    effective_date: '生效日期',
    node_information: '节点信息',
    max_nodes_num: '节点数上限'
  },
  baseline: {
    name: '基线名称',
    type: '基线类型',
    owner: '责任人',
    project_name: '项目名称',
    workflow_name: '工作流名称',
    alert_to: '告警接收人',
    is_open: '是否开启',
    timing: '定时时间',
    promise_time: '承诺时间',
    operation: '操作',
    edit: '编辑',
    on: '开启',
    off: '关闭',
    delete: '删除',
    detail: '详情',
    create_baseline: '创建基线',
    edit_baseline: '编辑基线',
    status: '基线状态',
    margin: '余量',
    task_name: '任务名称',
    task_type: '任务类型',
    estimated_time: '预计完成时间',
    support_task: '保障任务',
    baseline_name_tips: '请输入基线名称',
    project_tips: '请选择项目',
    workflow_tips: '请选择工作流',
    owner_tips: '请选择责任人',
    alert_tips: '请选择告警接收人',
    promise_time_tips: '请配置承诺时间',
    margin_tips: '请输入预警余量',
    minute: '分钟',
    every_day: '每天',
    alarm_margin: '预警余量',
    return: '返回',
    baseline_instance: '基线实例',
    baseline_instance_detail: '基线实例详情',
    dealer: '处理人',
    baseline_task: '基线任务',
    gantt: '甘特图',
    baseline_promise: '基线承诺',
    base_info: '基本信息',
    baseline_alarm: '基线预警',
    baseline_deal: '基线处理',
    deal: '处理',
    daily_baseline: '天基线',
    save: '安全',
    dangerous: '预警',
    break_line: '破线',
    all_status: '全部状态',
    workflow_instance_name: '工作流实例名称'
  },
  system_param: {
    add: '添加',
    search_input_tips: '请输入关键字',
    key: '参数键',
    value: '值',
    description: '描述',
    operation: '操作',
    confirm: '确认',
    cancel: '取消',
    edit: '编辑',
    delete: '删除',
    key_tips: '请处输入参数键',
    value_tips: '请输入值',
    system_biz_date: '日常调度实例定时的定时时间前一天，格式为 yyyyMMdd',
    system_biz_curdate: '日常调度实例定时的定时时间，格式为 yyyyMMdd',
    system_datetime: '日常调度实例定时的定时时间，格式为 yyyyMMddHHmmss',
    system_project_code: '项目ID',
    system_project_name: '项目名称',
    system_workflow_code: '工作流ID',
    system_workflow_name: '工作流名称',
    system_task_code: '任务ID',
    system_task_name: '任务名称',
    yyyy_mm_dd_hh_mm_ss:
      '自定义时间格式组合，支持分解组合，如：$[yyyyMMdd], $[HHmmss], $[yyyy-MM-dd] 等',
    suffix_n_year: '表示后 N 年，N 值支持自定义数值',
    prefix_n_year: '表示前 N 年，N 值支持自定义数值',
    suffix_n_month: '表示后 N 月，N 值支持自定义数值',
    prefix_n_month: '表示前 N 月，N 值支持自定义数值',
    suffix_n_week: '表示后 N 周，N 值支持自定义数值',
    prefix_n_week: '表示前 N 周，N 值支持自定义数值',
    suffix_n_day: '表示后 N 天，N 值支持自定义数值',
    prefix_n_day: '表示前 N 天，N 值支持自定义数值',
    suffix_n_hour: '表示后 N 小时，N 值支持自定义数值',
    prefix_n_hour: '表示前 N 小时，N 值支持自定义数值',
    suffix_n_minute: '表示后 N 分钟，N 值支持自定义数值',
    prefix_n_minute: '表示前 N 分钟，N 值支持自定义数值',
    var: '变量说明',
    var_base_time: '替换自定义基准时间yyyyMMdd',
    var_format:
      '替换自定义时间格式：yyyy-MM-dd HH:mm:ss、yyyy-MM-dd HH:mm、yyyy-MM-dd、yyyy-MM、yyyyMM',
    var_n:
      '自然日/交易日函数的N设为0,；range函数时，设为字母N表示每周/月/季度；N为整数时，-N表示前N周/月/季度，0表示当前周/月/季度，N表示后N周/月/季度；',
    var_m:
      'begin函数时交易日/自然日 M为所有整数，其他函数 M为正整数，例：-M表示前第 M天, 0表示当天, M表示后第 M天；range函数时 M为不等于 0的整数(括号外的 M为固定标识), 例：-M表示周/月/季度的后 M天, 0表示结果为空, M表示周/月/季度的前 M天；',
    var_calendar: '交易日历名称',
    natural_begin_m:
      '自然日函数计算，用于计算基准时间的前/后 第 M 天 例：$[natural_begin_M(20220628,yyyy-MM-dd,0,0)] 表示 当前自然日：2022-06-28 $[natural_begin_M(20220628,yyyy-MM-dd,0,1)] 表示 下一个自然日：2022-06-29 108$[natural_begin_M(20220628,yyyy-MM-dd,0,-1)] 表示 上一个自然日：2022-06-27',
    basic_param: '基础内置参数',
    derive_param: '衍生内置参数',
    calendar_function_param: '日历函数值',
    param_key_title_tips:
      // eslint-disable-next-line quotes
      "可在工作流任务脚本中通过{'$'}{'{'}参数键{'}'}直接引用参数变量",
    param_value_title_tips:
      // eslint-disable-next-line quotes
      "赋予常量请直接填写字符串，赋予函数变量请添加格式{'$'}{'{'}{'}'}",
    basic_param_tips: '系统内置的基础参数值，为其赋予参数键名称后使用',
    derive_param_tips: '系统内置的衍生参数值，替换变量后并赋予参数键名称使用',
    calendar_function_param_tips:
      '系统内置的日历函数，替换变量后并赋予参数键名称使用',
    handle_tips: '参数键和值不能为空',
    workflow_name: '工作流名称',
    task_name: '任务名称',
    own_project: '所属项目',
    executor: '执行用户',
    begin_time: '开始时间',
    end_time: '结束时间',
    updater: '更新者',
    allow_update: '允许任务修改',
    update_time: '更新时间'
  }
}
