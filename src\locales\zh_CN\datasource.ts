/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  id: 'Id',
  datasource: '数据源',
  create_datasource: '创建源',
  choose_datasource_type: '选择源类型',
  search:'搜索',
  search_input_tips: '请输入关键字',
  select: '更改',
  datasource_name: '源名称',
  datasource_name_tips: '请输入数据源名称',
  datasource_user_name: '所属用户',
  datasource_type: '源类型',
  datasource_parameter: '参数',
  description: '描述',
  description_tips: '请输入描述',
  create_time: '创建时间',
  update_time: '更新时间',
  operation: '操作',
  click_to_view: '点击查看',
  delete: '删除',
  delete_confirm: '确认删除？',
  confirm: '确定',
  cancel: '取消',
  create: '创建',
  edit: '编辑',
  edit_datasource: '编辑源',
  success: '成功',
  test_connect: '测试连接',
  test_connect_success: '测试连接成功',
  ip: 'IP主机名',
  ip_tips: '请输入IP主机名',
  port: '端口',
  port_tips: '请输入端口',
  database_name: '数据库名',
  database_name_tips: '请输入数据库名',
  oracle_connect_type: '服务名或SID',
  oracle_connect_type_tips: '请选择服务名或SID',
  oracle_service_name: '服务名',
  oracle_sid: 'SID',
  jdbc_connect_parameters: 'jdbc连接参数',
  principal_tips: '请输入Principal',
  authentication_type: '验证方式',
  dm_server_authentication: '达梦服务器验证',
  ldap_authentication: 'LDAP验证',
  operating_system_authentication: '操作系统验证',
  kerberos_authentication: 'Kerberos验证',
  krb5_conf_tips: '请输入kerberos认证参数 java.security.krb5.conf',
  keytab_username_tips: '请输入kerberos认证参数 login.user.keytab.username',
  keytab_path_tips: '请输入kerberos认证参数 login.user.keytab.path',
  format_tips: '请输入格式为',
  connection_parameter: '连接参数',
  user_name: '用户名',
  user_name_tips: '请输入用户名',
  user_name_tips1: '请输入“/”+用户名，例：/username',
  user_name_tips2: '请输入“//”+用户名，例：//username',
  user_name_tips3: '请输入“//”+用户名，例：///username',
  user_password: '密码',
  user_password_tips: '请输入密码',
  jdbc_format_tips: 'jdbc连接参数不是一个正确的JSON格式',
  all: '所有',
  warning: '警告',
  close_confirm_tips: '此操作会丢失当前正在创建的源',
  database: '传统数据库',
  file: '文件',
  no_structured: '非结构化',
  storage: '存储',
  data_analysis: '数据分析',
  remote_connection: '远程连接',
  fake_connection: '假连接'
}
