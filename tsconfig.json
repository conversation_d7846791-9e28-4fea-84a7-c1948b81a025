{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "noImplicitAny": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "skipLibCheck": true, "types": ["vite/client"], "plugins": [{"name": "typescript-plugin-css-modules"}]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}