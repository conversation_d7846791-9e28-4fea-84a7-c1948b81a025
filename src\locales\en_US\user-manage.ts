/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  id: 'Id',
  user_manage: 'User Manage',
  create: 'Create',
  username: 'Userna<PERSON>',
  state: 'State',
  create_time: 'Create Time',
  update_time: 'Update Time',
  operation: 'Operation',
  enable: 'Enable',
  disable: 'Disable',
  edit: 'Edit',
  delete: 'Delete',
  active: 'Active',
  inactive: 'Inactive',
  password: 'Password',
  model_validate_tips: 'Required Fields',
  username_tips: 'Required fields, number, letter case, 50 characters',
  password_tips: 'Required fields, number, letter case, 6 characters',
  user_delete_tips:
    'Whether to delete the user? It cannot be restored after being deleted'
}
