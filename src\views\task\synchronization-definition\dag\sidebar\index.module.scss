/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.task-container {
  width: 250px;
  flex-shrink: 0;
  overflow-y: auto;
}

.task-item {
  cursor: pointer;
  padding: 1px 15px;
  border: 1px solid #eee;
  border-radius: 5px;
  height: 36px;
  display: flex;
  align-items: center;
  .task-image {
    width: 20px;
    height: 20px;
    // margin-top: 6px;
  }
}

h3 {
  margin: 0;
}