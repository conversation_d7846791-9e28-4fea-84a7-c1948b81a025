/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  virtual_tables: 'Virtual Tables',
  create_virtual_tables: 'Create Virtual Tables',
  edit_virtual_tables: 'Edit Virtual Tables',
  source_type: 'Source Type',
  source_type_tips: 'Please select a source type',
  source_name: 'Source Name',
  source_name_tips: 'Please enter source name',
  table_name: 'Table Name',
  database_name: 'Database Name',
  creator: 'Creator',
  creation_time: 'Creation Time',
  updater: 'Updater',
  update_time: 'Update Time',
  operation: 'Operation',
  edit: 'Edit',
  delete: 'Delete',
  confirm: 'Confirm',
  delete_confirm: 'Delete?',
  cancel: 'Cancel',
  configure: 'Configure',
  model: 'Model',
  complete: 'Complete',
  virtual_tables_name: 'Virtual Table Name',
  virtual_tables_name_tips: 'Please enter a virtual table name',
  next_step: 'Next Step',
  previous_step: 'Previous Step',
  table_structure: 'Table Structure',
  add: 'Add a row',
  field_name: 'Field Name',
  field_name_tips: 'Please enter a field name',
  field_type: 'Field Type',
  is_null: 'Null',
  is_primary_key: 'Primary Key',
  description: 'Field Description',
  yes: 'Yes',
  no: 'No',
  warning: 'Warning',
  close_confirm_tips:
    'This operation will lose the currently created virtual table',
  save_data_tips: 'Please save the data in the table',
  table_data_required_tips: 'Please add a record to the table',
  default_value: 'Default Value',
  create: 'Create',
  search: 'Search'
}
